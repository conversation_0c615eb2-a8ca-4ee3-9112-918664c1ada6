'use client';

import Link from 'next/link';
import { useAuthStore } from '@/store/authStore';

const quickActions = [
  {
    id: 'chatrooms',
    title: 'Join <PERSON><PERSON>',
    description: 'Connect with others in safe, supportive spaces',
    href: '/chatrooms',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-2-2V10a2 2 0 012-2h2m2-4h6a2 2 0 012 2v6a2 2 0 01-2 2h-6l-4 4V8a2 2 0 012-2z" />
      </svg>
    ),
    color: 'blue',
    available: true,
  },
  {
    id: 'resources',
    title: 'Self-Help Library',
    description: 'Explore articles, videos, and wellness tools',
    href: '/resources',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
      </svg>
    ),
    color: 'green',
    available: true,
  },
  {
    id: 'counselors',
    title: 'Book a Session',
    description: 'Schedule 1-on-1 time with certified counselors',
    href: '/counselors',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
      </svg>
    ),
    color: 'purple',
    available: false, // Only for authenticated users
  },
  {
    id: 'mood-tracker',
    title: 'Mood Tracker',
    description: 'Track your daily mood and emotional patterns',
    href: '/mood-tracker',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
    color: 'yellow',
    available: false, // Only for authenticated users
  },
];

const colorClasses = {
  blue: {
    bg: 'bg-blue-50',
    icon: 'text-blue-600',
    hover: 'hover:bg-blue-100',
  },
  green: {
    bg: 'bg-green-50',
    icon: 'text-green-600',
    hover: 'hover:bg-green-100',
  },
  purple: {
    bg: 'bg-purple-50',
    icon: 'text-purple-600',
    hover: 'hover:bg-purple-100',
  },
  yellow: {
    bg: 'bg-yellow-50',
    icon: 'text-yellow-600',
    hover: 'hover:bg-yellow-100',
  },
};

export default function QuickActionsWidget() {
  const { isAuthenticated } = useAuthStore();

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {quickActions.map((action) => {
          const isAvailable = action.available || isAuthenticated;
          const colors = colorClasses[action.color as keyof typeof colorClasses];
          
          if (isAvailable) {
            return (
              <Link
                key={action.id}
                href={action.href}
                className={`${colors.bg} ${colors.hover} p-4 rounded-lg transition-colors group`}
              >
                <div className="flex items-start space-x-3">
                  <div className={`${colors.icon} flex-shrink-0 mt-1`}>
                    {action.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 group-hover:text-gray-700">
                      {action.title}
                    </h4>
                    <p className="text-sm text-gray-600 mt-1">
                      {action.description}
                    </p>
                  </div>
                </div>
              </Link>
            );
          }
          
          return (
            <div
              key={action.id}
              className="bg-gray-50 p-4 rounded-lg border-2 border-dashed border-gray-300"
            >
              <div className="flex items-start space-x-3">
                <div className="text-gray-400 flex-shrink-0 mt-1">
                  {action.icon}
                </div>
                <div>
                  <h4 className="font-medium text-gray-500">
                    {action.title}
                  </h4>
                  <p className="text-sm text-gray-400 mt-1">
                    {action.description}
                  </p>
                  <Link
                    href="/auth/register"
                    className="text-purple-600 hover:text-purple-500 text-sm font-medium mt-2 inline-block"
                  >
                    Sign up to unlock →
                  </Link>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
