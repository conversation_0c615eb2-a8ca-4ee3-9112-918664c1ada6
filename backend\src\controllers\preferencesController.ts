import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { PreferencesService } from '@/services/preferencesService';
import { createError } from '@/middleware/errorHandler';

export class PreferencesController {
  /**
   * Get user preferences
   */
  static async getPreferences(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const preferences = await PreferencesService.getUserPreferences(req.user._id.toString());

      res.json({
        success: true,
        data: { preferences }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update notification preferences
   */
  static async updateNotificationPreferences(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400));
      }

      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const preferences = await PreferencesService.updateNotificationPreferences(
        req.user._id.toString(),
        req.body
      );

      res.json({
        success: true,
        message: 'Notification preferences updated successfully',
        data: { preferences }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update privacy preferences
   */
  static async updatePrivacyPreferences(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400));
      }

      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const preferences = await PreferencesService.updatePrivacyPreferences(
        req.user._id.toString(),
        req.body
      );

      res.json({
        success: true,
        message: 'Privacy preferences updated successfully',
        data: { preferences }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update all preferences
   */
  static async updateAllPreferences(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400));
      }

      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const preferences = await PreferencesService.updateAllPreferences(
        req.user._id.toString(),
        req.body
      );

      res.json({
        success: true,
        message: 'Preferences updated successfully',
        data: { preferences }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reset preferences to default
   */
  static async resetPreferences(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const preferences = await PreferencesService.resetPreferences(req.user._id.toString());

      res.json({
        success: true,
        message: 'Preferences reset to default successfully',
        data: { preferences }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Export user preferences (GDPR compliance)
   */
  static async exportPreferences(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const preferences = await PreferencesService.exportUserPreferences(req.user._id.toString());

      res.json({
        success: true,
        data: { preferences }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Import user preferences
   */
  static async importPreferences(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400));
      }

      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const preferences = await PreferencesService.importUserPreferences(
        req.user._id.toString(),
        req.body
      );

      res.json({
        success: true,
        message: 'Preferences imported successfully',
        data: { preferences }
      });
    } catch (error) {
      next(error);
    }
  }
}
