import { Message, IMessage } from '@/models/Message';
import { ChatRoom } from '@/models/ChatRoom';
import { User } from '@/models/User';
import { Report } from '@/models/Admin';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

export interface ModerationResult {
  blocked: boolean;
  reason?: string;
  suggestions?: string[];
  confidence: number;
}

export interface ReportData {
  reportedBy: string;
  targetId: string;
  targetType: 'message' | 'user';
  reason: string;
  description: string;
  evidence?: {
    screenshots?: string[];
    additionalInfo?: any;
  };
}

export class ModerationService {
  // Profanity filter words (basic implementation)
  private static readonly PROFANITY_WORDS = [
    'spam', 'scam', 'fake', 'fraud', 'hate', 'abuse',
    // Add more words as needed
  ];

  // Inappropriate patterns
  private static readonly INAPPROPRIATE_PATTERNS = [
    /\b(?:https?:\/\/)?(?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}\b/g, // URLs
    /\b\d{10,}\b/g, // Phone numbers
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email addresses
  ];

  /**
   * Moderate content for inappropriate material
   */
  static async moderateContent(content: string): Promise<ModerationResult> {
    try {
      const result: ModerationResult = {
        blocked: false,
        confidence: 0,
        suggestions: []
      };

      const lowerContent = content.toLowerCase();

      // Check for profanity
      const profanityFound = this.PROFANITY_WORDS.some(word => 
        lowerContent.includes(word.toLowerCase())
      );

      if (profanityFound) {
        result.blocked = true;
        result.reason = 'Contains inappropriate language';
        result.confidence = 0.8;
        result.suggestions = ['Please use respectful language'];
        return result;
      }

      // Check for inappropriate patterns
      for (const pattern of this.INAPPROPRIATE_PATTERNS) {
        if (pattern.test(content)) {
          result.blocked = true;
          result.reason = 'Contains prohibited content (URLs, phone numbers, or email addresses)';
          result.confidence = 0.9;
          result.suggestions = ['Please avoid sharing personal contact information'];
          return result;
        }
      }

      // Check for excessive caps (shouting)
      const capsRatio = (content.match(/[A-Z]/g) || []).length / content.length;
      if (capsRatio > 0.7 && content.length > 10) {
        result.blocked = true;
        result.reason = 'Excessive use of capital letters';
        result.confidence = 0.6;
        result.suggestions = ['Please avoid using all caps'];
        return result;
      }

      // Check for repeated characters (spam-like)
      const repeatedPattern = /(.)\1{4,}/g;
      if (repeatedPattern.test(content)) {
        result.blocked = true;
        result.reason = 'Contains spam-like repeated characters';
        result.confidence = 0.7;
        result.suggestions = ['Please avoid repeating characters excessively'];
        return result;
      }

      return result;
    } catch (error) {
      logger.error('Content moderation error:', error);
      return {
        blocked: false,
        confidence: 0
      };
    }
  }

  /**
   * Report a message or user
   */
  static async reportContent(reportData: ReportData): Promise<void> {
    try {
      const report = new Report({
        reportedBy: reportData.reportedBy,
        reportType: reportData.targetType === 'message' ? 'message' : 'user',
        targetId: reportData.targetId,
        targetType: reportData.targetType,
        reason: reportData.reason,
        description: reportData.description,
        evidence: reportData.evidence || {},
        status: 'pending',
        priority: this.calculateReportPriority(reportData.reason)
      });

      await report.save();

      // Auto-moderate based on report type
      if (reportData.targetType === 'message') {
        await this.handleMessageReport(reportData.targetId, reportData.reason);
      }

      logger.info(`Content reported: ${reportData.targetType} ${reportData.targetId} by user ${reportData.reportedBy}`);
    } catch (error) {
      logger.error('Report content error:', error);
      throw error;
    }
  }

  /**
   * Hide a message (moderator action)
   */
  static async hideMessage(messageId: string, moderatorId: string, reason: string): Promise<void> {
    try {
      const message = await Message.findById(messageId);
      
      if (!message) {
        throw createError('Message not found', 404);
      }

      message.moderation.isHidden = true;
      message.moderation.hiddenBy = moderatorId as any;
      message.moderation.hiddenAt = new Date();
      message.moderation.hiddenReason = reason;

      await message.save();
      logger.info(`Message hidden: ${messageId} by moderator: ${moderatorId}`);
    } catch (error) {
      logger.error('Hide message error:', error);
      throw error;
    }
  }

  /**
   * Delete a message (moderator action)
   */
  static async deleteMessage(messageId: string, moderatorId: string): Promise<void> {
    try {
      const message = await Message.findById(messageId);
      
      if (!message) {
        throw createError('Message not found', 404);
      }

      message.moderation.isDeleted = true;
      message.moderation.deletedBy = moderatorId as any;
      message.moderation.deletedAt = new Date();

      await message.save();
      logger.info(`Message deleted: ${messageId} by moderator: ${moderatorId}`);
    } catch (error) {
      logger.error('Delete message error:', error);
      throw error;
    }
  }

  /**
   * Mute a user in a chat room
   */
  static async muteUser(
    roomId: string,
    userId: string,
    moderatorId: string,
    duration?: number, // in minutes
    reason?: string
  ): Promise<void> {
    try {
      const chatRoom = await ChatRoom.findById(roomId);
      
      if (!chatRoom) {
        throw createError('Chat room not found', 404);
      }

      const participant = chatRoom.participants.find(p => 
        p.userId?.toString() === userId
      );

      if (!participant) {
        throw createError('User not found in chat room', 404);
      }

      participant.isMuted = true;
      if (duration) {
        participant.mutedUntil = new Date(Date.now() + duration * 60 * 1000);
      }

      await chatRoom.save();
      logger.info(`User muted: ${userId} in room: ${roomId} by moderator: ${moderatorId}`);
    } catch (error) {
      logger.error('Mute user error:', error);
      throw error;
    }
  }

  /**
   * Unmute a user in a chat room
   */
  static async unmuteUser(roomId: string, userId: string, moderatorId: string): Promise<void> {
    try {
      const chatRoom = await ChatRoom.findById(roomId);
      
      if (!chatRoom) {
        throw createError('Chat room not found', 404);
      }

      const participant = chatRoom.participants.find(p => 
        p.userId?.toString() === userId
      );

      if (!participant) {
        throw createError('User not found in chat room', 404);
      }

      participant.isMuted = false;
      participant.mutedUntil = undefined;

      await chatRoom.save();
      logger.info(`User unmuted: ${userId} in room: ${roomId} by moderator: ${moderatorId}`);
    } catch (error) {
      logger.error('Unmute user error:', error);
      throw error;
    }
  }

  /**
   * Get moderation queue (pending reports)
   */
  static async getModerationQueue(options: any = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        status = 'pending',
        priority,
        reportType
      } = options;

      const query: any = { status };
      if (priority) query.priority = priority;
      if (reportType) query.reportType = reportType;

      const skip = (page - 1) * limit;

      const [reports, total] = await Promise.all([
        Report.find(query)
          .populate('reportedBy', 'firstName lastName email')
          .populate('assignedTo', 'firstName lastName')
          .sort({ priority: -1, createdAt: -1 })
          .skip(skip)
          .limit(limit),
        Report.countDocuments(query)
      ]);

      return {
        reports,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      };
    } catch (error) {
      logger.error('Get moderation queue error:', error);
      throw error;
    }
  }

  /**
   * Resolve a report
   */
  static async resolveReport(
    reportId: string,
    moderatorId: string,
    action: string,
    description: string
  ): Promise<void> {
    try {
      const report = await Report.findById(reportId);
      
      if (!report) {
        throw createError('Report not found', 404);
      }

      report.status = 'resolved';
      report.resolution = {
        action,
        description,
        resolvedBy: moderatorId as any,
        resolvedAt: new Date()
      };

      await report.save();
      logger.info(`Report resolved: ${reportId} by moderator: ${moderatorId}`);
    } catch (error) {
      logger.error('Resolve report error:', error);
      throw error;
    }
  }

  /**
   * Calculate report priority based on reason
   */
  private static calculateReportPriority(reason: string): 'low' | 'medium' | 'high' | 'urgent' {
    const urgentReasons = ['harassment', 'safety_concern', 'inappropriate_content'];
    const highReasons = ['spam', 'fake_profile', 'unprofessional_conduct'];
    const mediumReasons = ['technical_issue', 'billing_issue'];

    if (urgentReasons.includes(reason)) return 'urgent';
    if (highReasons.includes(reason)) return 'high';
    if (mediumReasons.includes(reason)) return 'medium';
    return 'low';
  }

  /**
   * Handle automatic actions for message reports
   */
  private static async handleMessageReport(messageId: string, reason: string): Promise<void> {
    try {
      // Auto-hide messages for certain reasons
      const autoHideReasons = ['inappropriate_content', 'harassment', 'spam'];
      
      if (autoHideReasons.includes(reason)) {
        const message = await Message.findById(messageId);
        if (message) {
          message.moderation.isHidden = true;
          message.moderation.hiddenAt = new Date();
          message.moderation.hiddenReason = `Auto-hidden due to report: ${reason}`;
          await message.save();
          
          logger.info(`Message auto-hidden due to report: ${messageId}`);
        }
      }
    } catch (error) {
      logger.error('Handle message report error:', error);
    }
  }

  /**
   * Get user's moderation history
   */
  static async getUserModerationHistory(userId: string) {
    try {
      const [reports, messages] = await Promise.all([
        Report.find({
          $or: [
            { reportedBy: userId },
            { targetId: userId, targetType: 'user' }
          ]
        }).sort({ createdAt: -1 }).limit(20),
        
        Message.find({
          senderId: userId,
          $or: [
            { 'moderation.isHidden': true },
            { 'moderation.isDeleted': true }
          ]
        }).sort({ createdAt: -1 }).limit(20)
      ]);

      return {
        reports,
        moderatedMessages: messages,
        totalReports: reports.length,
        totalModeratedMessages: messages.length
      };
    } catch (error) {
      logger.error('Get user moderation history error:', error);
      throw error;
    }
  }
}
