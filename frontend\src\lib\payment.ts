// Payment API functions
import { 
  PaymentInitialization, 
  PaymentResponse, 
  PaymentVerification, 
  PaymentTransaction,
  PaymentHistory,
  PaymentStats,
  PaymentPricing,
  RefundRequest,
  RefundResponse 
} from '@/types/payment';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

class PaymentAPI {
  private getHeaders(token?: string) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }

  // Payment Initialization
  async initializePayment(sessionId: string, token: string): Promise<{
    success: boolean;
    message: string;
    data: PaymentResponse;
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/payment/initialize`, {
      method: 'POST',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to initialize payment');
    }

    return response.json();
  }

  // Payment Verification
  async verifyPayment(reference: string): Promise<{
    success: boolean;
    message: string;
    data: { session: any };
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/payment/verify/${reference}`, {
      method: 'GET',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to verify payment');
    }

    return response.json();
  }

  // Pricing Calculation
  async calculatePricing(
    counselorId: string,
    duration: number,
    currency: 'NGN' | 'USD',
    token?: string
  ): Promise<{
    success: boolean;
    data: PaymentPricing;
  }> {
    const queryParams = new URLSearchParams({
      counselorId,
      duration: duration.toString(),
      currency,
    });

    const response = await fetch(`${API_BASE_URL}/sessions/pricing?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to calculate pricing');
    }

    return response.json();
  }

  // Payment History
  async getPaymentHistory(
    page: number = 1,
    limit: number = 20,
    token: string
  ): Promise<{
    success: boolean;
    data: {
      payments: PaymentHistory[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await fetch(`${API_BASE_URL}/payments/history?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch payment history');
    }

    return response.json();
  }

  // Payment Statistics
  async getPaymentStats(token: string): Promise<{
    success: boolean;
    data: PaymentStats;
  }> {
    const response = await fetch(`${API_BASE_URL}/payments/stats`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch payment statistics');
    }

    return response.json();
  }

  // Refund Request
  async requestRefund(
    sessionId: string,
    reason: string,
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/refund`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to request refund');
    }

    return response.json();
  }

  // Get Supported Banks
  async getSupportedBanks(): Promise<{
    success: boolean;
    data: {
      banks: Array<{
        name: string;
        code: string;
        longcode: string;
        gateway: string;
        pay_with_bank: boolean;
        active: boolean;
        country: string;
        currency: string;
        type: string;
        id: number;
        createdAt: string;
        updatedAt: string;
      }>;
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/banks`, {
      method: 'GET',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch supported banks');
    }

    return response.json();
  }

  // Transaction Details
  async getTransaction(transactionId: string, token: string): Promise<{
    success: boolean;
    data: PaymentTransaction;
  }> {
    const response = await fetch(`${API_BASE_URL}/payments/transaction/${transactionId}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch transaction details');
    }

    return response.json();
  }

  // Cancel Payment
  async cancelPayment(sessionId: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/payment/cancel`, {
      method: 'POST',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to cancel payment');
    }

    return response.json();
  }
}

export const paymentAPI = new PaymentAPI();

// Payment utilities
export class PaymentUtils {
  static formatCurrency(amount: number, currency: 'NGN' | 'USD'): string {
    const symbol = currency === 'NGN' ? '₦' : '$';
    const formattedAmount = (amount / 100).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
    return `${symbol}${formattedAmount}`;
  }

  static generatePaymentReference(prefix: string = 'TH'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}_${timestamp}_${random}`;
  }

  static validateAmount(amount: number, currency: 'NGN' | 'USD'): {
    isValid: boolean;
    error?: string;
  } {
    const limits = {
      NGN: { min: 100, max: 10000000 }, // ₦1.00 to ₦100,000.00
      USD: { min: 100, max: 25000000 }, // $1.00 to $250,000.00
    };

    const limit = limits[currency];
    
    if (amount < limit.min) {
      return {
        isValid: false,
        error: `Minimum amount is ${PaymentUtils.formatCurrency(limit.min, currency)}`,
      };
    }

    if (amount > limit.max) {
      return {
        isValid: false,
        error: `Maximum amount is ${PaymentUtils.formatCurrency(limit.max, currency)}`,
      };
    }

    return { isValid: true };
  }

  static getPaymentStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return 'text-green-600 bg-green-100';
      case 'pending':
      case 'processing':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
      case 'error':
        return 'text-red-600 bg-red-100';
      case 'cancelled':
        return 'text-gray-600 bg-gray-100';
      case 'refunded':
      case 'partially-refunded':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  }

  static getPaymentStatusIcon(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return '✅';
      case 'pending':
      case 'processing':
        return '⏳';
      case 'failed':
      case 'error':
        return '❌';
      case 'cancelled':
        return '🚫';
      case 'refunded':
      case 'partially-refunded':
        return '↩️';
      default:
        return '❓';
    }
  }

  static calculatePlatformFee(amount: number, feePercentage: number = 10): number {
    return Math.round(amount * (feePercentage / 100));
  }

  static calculatePaymentFee(amount: number, channel: string = 'card'): number {
    // Paystack fee structure (approximate)
    const fees = {
      card: { percentage: 1.5, fixed: 100, cap: 200000 }, // 1.5% + ₦1.00, capped at ₦2000
      bank: { percentage: 1.4, fixed: 100, cap: 200000 },
      ussd: { percentage: 0, fixed: 5000, cap: 5000 }, // Flat ₦50
      transfer: { percentage: 0, fixed: 5000, cap: 5000 },
    };

    const feeStructure = fees[channel as keyof typeof fees] || fees.card;
    const percentageFee = Math.round(amount * (feeStructure.percentage / 100));
    const totalFee = percentageFee + feeStructure.fixed;
    
    return Math.min(totalFee, feeStructure.cap);
  }

  static isPaymentExpired(expiresAt: string): boolean {
    return new Date(expiresAt) < new Date();
  }

  static getTimeUntilExpiry(expiresAt: string): string {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diff = expiry.getTime() - now.getTime();

    if (diff <= 0) return 'Expired';

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  }

  static openPaystackPopup(
    paymentUrl: string,
    onSuccess: (reference: string) => void,
    onCancel: () => void,
    onError: (error: any) => void
  ): void {
    // Check if Paystack is loaded
    if (typeof window !== 'undefined' && (window as any).PaystackPop) {
      const handler = (window as any).PaystackPop.setup({
        key: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,
        email: '', // Will be set by backend
        amount: 0, // Will be set by backend
        currency: 'NGN',
        ref: '', // Will be set by backend
        callback: (response: any) => {
          onSuccess(response.reference);
        },
        onClose: () => {
          onCancel();
        },
      });

      handler.openIframe();
    } else {
      // Fallback to redirect
      window.location.href = paymentUrl;
    }
  }

  static loadPaystackScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof window !== 'undefined' && (window as any).PaystackPop) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.paystack.co/v1/inline.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Paystack script'));
      document.head.appendChild(script);
    });
  }
}
