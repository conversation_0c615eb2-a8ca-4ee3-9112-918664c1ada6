// Admin panel API functions
import { 
  <PERSON>min<PERSON><PERSON><PERSON>, 
  Admin<PERSON>ser, 
  AdminCounselor, 
  AdminSession, 
  AdminReport, 
  AdminAction,
  AdminFilters, 
  AdminListOptions,
  PlatformMetrics 
} from '@/types/admin';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

class AdminAPI {
  private getHeaders(token?: string) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }

  // Dashboard & Statistics
  async getDashboardStats(token: string): Promise<{
    success: boolean;
    data: AdminStats;
  }> {
    const response = await fetch(`${API_BASE_URL}/admin/stats/dashboard`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch dashboard stats');
    }

    return response.json();
  }

  async getPlatformMetrics(period: 'week' | 'month' | 'year' = 'month', token: string): Promise<{
    success: boolean;
    data: PlatformMetrics;
  }> {
    const response = await fetch(`${API_BASE_URL}/admin/metrics?period=${period}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch platform metrics');
    }

    return response.json();
  }

  // User Management
  async getUsers(
    filters: AdminFilters = {},
    options: AdminListOptions = {},
    token: string
  ): Promise<{
    success: boolean;
    data: {
      users: AdminUser[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    
    if (options.page) queryParams.append('page', options.page.toString());
    if (options.limit) queryParams.append('limit', options.limit.toString());
    if (options.sortBy) queryParams.append('sortBy', options.sortBy);
    if (options.sortOrder) queryParams.append('sortOrder', options.sortOrder);
    
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.role) queryParams.append('role', filters.role);
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.dateRange) {
      queryParams.append('startDate', filters.dateRange.start);
      queryParams.append('endDate', filters.dateRange.end);
    }

    const response = await fetch(`${API_BASE_URL}/users?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch users');
    }

    return response.json();
  }

  async updateUserRole(userId: string, role: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/users/${userId}/role`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ role }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update user role');
    }

    return response.json();
  }

  async deactivateUser(userId: string, reason: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/deactivate`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to deactivate user');
    }

    return response.json();
  }

  async reactivateUser(userId: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/users/${userId}/reactivate`, {
      method: 'PUT',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to reactivate user');
    }

    return response.json();
  }

  // Counselor Management
  async getPendingCounselors(token: string): Promise<{
    success: boolean;
    data: { counselors: AdminCounselor[] };
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/admin/pending`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch pending counselors');
    }

    return response.json();
  }

  async getCounselors(
    filters: AdminFilters = {},
    options: AdminListOptions = {},
    token: string
  ): Promise<{
    success: boolean;
    data: {
      counselors: AdminCounselor[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    
    if (options.page) queryParams.append('page', options.page.toString());
    if (options.limit) queryParams.append('limit', options.limit.toString());
    if (options.sortBy) queryParams.append('sortBy', options.sortBy);
    if (options.sortOrder) queryParams.append('sortOrder', options.sortOrder);
    
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.status) queryParams.append('status', filters.status);

    const response = await fetch(`${API_BASE_URL}/admin/counselors?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch counselors');
    }

    return response.json();
  }

  async approveCounselor(counselorId: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/${counselorId}/approve`, {
      method: 'PUT',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to approve counselor');
    }

    return response.json();
  }

  async rejectCounselor(counselorId: string, reason: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/${counselorId}/reject`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to reject counselor');
    }

    return response.json();
  }

  async suspendCounselor(counselorId: string, reason: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/admin/counselors/${counselorId}/suspend`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to suspend counselor');
    }

    return response.json();
  }

  // Session Management
  async getSessions(
    filters: AdminFilters = {},
    options: AdminListOptions = {},
    token: string
  ): Promise<{
    success: boolean;
    data: {
      sessions: AdminSession[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    
    if (options.page) queryParams.append('page', options.page.toString());
    if (options.limit) queryParams.append('limit', options.limit.toString());
    if (options.sortBy) queryParams.append('sortBy', options.sortBy);
    if (options.sortOrder) queryParams.append('sortOrder', options.sortOrder);
    
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.dateRange) {
      queryParams.append('startDate', filters.dateRange.start);
      queryParams.append('endDate', filters.dateRange.end);
    }

    const response = await fetch(`${API_BASE_URL}/admin/sessions?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch sessions');
    }

    return response.json();
  }

  async refundSession(sessionId: string, reason: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/admin/sessions/${sessionId}/refund`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to refund session');
    }

    return response.json();
  }

  // Reports & Moderation
  async getReports(
    filters: AdminFilters = {},
    options: AdminListOptions = {},
    token: string
  ): Promise<{
    success: boolean;
    data: {
      reports: AdminReport[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    
    if (options.page) queryParams.append('page', options.page.toString());
    if (options.limit) queryParams.append('limit', options.limit.toString());
    if (options.sortBy) queryParams.append('sortBy', options.sortBy);
    if (options.sortOrder) queryParams.append('sortOrder', options.sortOrder);
    
    if (filters.status) queryParams.append('status', filters.status);

    const response = await fetch(`${API_BASE_URL}/admin/reports?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch reports');
    }

    return response.json();
  }

  async updateReportStatus(reportId: string, status: string, resolution?: string, token?: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/admin/reports/${reportId}/status`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ status, resolution }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update report status');
    }

    return response.json();
  }

  // Admin Actions & Audit Log
  async getAdminActions(
    filters: AdminFilters = {},
    options: AdminListOptions = {},
    token: string
  ): Promise<{
    success: boolean;
    data: {
      actions: AdminAction[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    
    if (options.page) queryParams.append('page', options.page.toString());
    if (options.limit) queryParams.append('limit', options.limit.toString());
    if (options.sortBy) queryParams.append('sortBy', options.sortBy);
    if (options.sortOrder) queryParams.append('sortOrder', options.sortOrder);
    
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.dateRange) {
      queryParams.append('startDate', filters.dateRange.start);
      queryParams.append('endDate', filters.dateRange.end);
    }

    const response = await fetch(`${API_BASE_URL}/admin/actions?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch admin actions');
    }

    return response.json();
  }
}

export const adminAPI = new AdminAPI();
