import { Router } from "express";
import { ContentController } from "@/controllers/contentController";
import { resourceValidations } from "@/utils/validation";
import {
  authenticate,
  optionalAuth,
  requireCounselor,
  requireAdmin,
} from "@/middleware/auth";
import { createUploadMiddleware, handleUploadError } from "@/middleware/upload";

const router = Router();

// Public routes
router.get("/", optionalAuth, ContentController.getContent);

router.get("/search", ContentController.searchContent);

router.get("/autocomplete", ContentController.getAutocomplete);

router.get("/trending", ContentController.getTrendingContent);

router.get("/categories", ContentController.getCategories);

router.get("/tags", ContentController.getPopularTags);

router.get("/popular-searches", ContentController.getPopularSearches);

router.get("/:identifier", optionalAuth, ContentController.getContentById);

router.get("/:contentId/similar", ContentController.getSimilarContent);

// Content creation and management (counselors and admins)
router.post(
  "/",
  authenticate,
  requireCounselor,
  resourceValidations.create,
  ContentController.createContent
);

router.put(
  "/:contentId",
  authenticate,
  requireCounselor,
  resourceValidations.update,
  ContentController.updateContent
);

router.delete(
  "/:contentId",
  authenticate,
  requireCounselor,
  ContentController.deleteContent
);

// Media upload
const uploadContentMedia = createUploadMiddleware({
  fieldName: "file",
  allowedTypes: ["image/*", "video/*", "audio/*", "application/pdf"],
  maxSize: 50 * 1024 * 1024, // 50MB
  maxFiles: 1,
});

router.post(
  "/upload",
  authenticate,
  requireCounselor,
  uploadContentMedia,
  handleUploadError,
  ContentController.uploadMedia
);

// User interactions
router.post("/:contentId/like", authenticate, ContentController.toggleLike);

router.post(
  "/:contentId/bookmark",
  authenticate,
  ContentController.toggleBookmark
);

router.post("/:contentId/rate", authenticate, ContentController.rateContent);

// User content management
router.get("/user/bookmarks", authenticate, ContentController.getUserBookmarks);

router.get(
  "/user/recommendations",
  authenticate,
  ContentController.getRecommendations
);

export default router;
