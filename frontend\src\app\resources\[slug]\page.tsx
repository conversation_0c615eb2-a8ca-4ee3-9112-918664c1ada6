'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { useAuthStore } from '@/store/authStore';
import { resourcesAPI } from '@/lib/resources';
import { Resource, RESOURCE_TYPES, DIFFICULTY_LEVELS } from '@/types/resources';
import Header from '@/components/layout/Header';
import ResourceCard from '@/components/resources/ResourceCard';

export default function ResourcePage() {
  const router = useRouter();
  const params = useParams();
  const slug = params.slug as string;
  
  const { isAuthenticated, isGuest, checkAuth, isLoading, tokens, guestToken, user } = useAuthStore();
  const [resource, setResource] = useState<Resource | null>(null);
  const [similarResources, setSimilarResources] = useState<Resource[]>([]);
  const [resourceLoading, setResourceLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [userRating, setUserRating] = useState(0);
  const [showRatingModal, setShowRatingModal] = useState(false);

  const token = tokens?.accessToken || guestToken;

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isGuest && !isLoading) {
      router.push('/');
    }
  }, [isAuthenticated, isGuest, isLoading, router]);

  useEffect(() => {
    if (slug && token) {
      fetchResource();
    }
  }, [slug, token]);

  const fetchResource = async () => {
    try {
      setResourceLoading(true);
      setError(null);
      
      const response = await resourcesAPI.getResource(slug, token);
      const resourceData = response.data.content;
      
      setResource(resourceData);
      
      // Check if user has bookmarked this resource
      if (user && resourceData.bookmarkedBy.includes(user._id)) {
        setIsBookmarked(true);
      }
      
      // Get user's rating if exists
      if (user) {
        const userRatingData = resourceData.ratings.find(r => r.userId === user._id);
        if (userRatingData) {
          setUserRating(userRatingData.rating);
        }
      }
      
      // Fetch similar resources
      const similarResponse = await resourcesAPI.getSimilarResources(resourceData._id, 4, token);
      setSimilarResources(similarResponse.data.content);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load resource');
    } finally {
      setResourceLoading(false);
    }
  };

  const handleBookmark = async () => {
    if (!isAuthenticated || !token || !resource) return;

    try {
      if (isBookmarked) {
        await resourcesAPI.unbookmarkResource(resource._id, token);
        setIsBookmarked(false);
      } else {
        await resourcesAPI.bookmarkResource(resource._id, token);
        setIsBookmarked(true);
      }
    } catch (error) {
      console.error('Bookmark error:', error);
    }
  };

  const handleRating = async (rating: number, review?: string) => {
    if (!isAuthenticated || !token || !resource) return;

    try {
      await resourcesAPI.rateResource(resource._id, rating, review, token);
      setUserRating(rating);
      setShowRatingModal(false);
      
      // Refresh resource to get updated ratings
      fetchResource();
    } catch (error) {
      console.error('Rating error:', error);
    }
  };

  if (isLoading || resourceLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!isAuthenticated && !isGuest) {
    return null; // Will redirect
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <svg className="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Error</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link
              href="/resources"
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
              Back to Resources
            </Link>
          </div>
        </main>
      </div>
    );
  }

  if (!resource) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Resource not found</h1>
            <p className="text-gray-600 mb-4">This resource may have been removed or you don't have access.</p>
            <Link
              href="/resources"
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
              Back to Resources
            </Link>
          </div>
        </main>
      </div>
    );
  }

  const typeInfo = RESOURCE_TYPES.find(t => t.value === resource.type);
  const difficultyInfo = DIFFICULTY_LEVELS.find(d => d.value === resource.difficulty);

  const formatDuration = () => {
    if (resource.type === 'article' && resource.estimatedReadTime) {
      return `${resource.estimatedReadTime} min read`;
    }
    if ((resource.type === 'video' || resource.type === 'audio') && resource.estimatedDuration) {
      return `${resource.estimatedDuration} min`;
    }
    return null;
  };

  const formatRating = (rating: number) => {
    return '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <Link href="/resources" className="text-gray-500 hover:text-gray-700">
                Resources
              </Link>
            </li>
            <li>
              <svg className="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </li>
            <li>
              <span className="text-gray-900 font-medium">{resource.title}</span>
            </li>
          </ol>
        </nav>

        {/* Resource Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-4">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                  {typeInfo?.icon} {typeInfo?.label}
                </span>
                
                {difficultyInfo && (
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    difficultyInfo.color === 'green' ? 'bg-green-100 text-green-800' :
                    difficultyInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {difficultyInfo.label}
                  </span>
                )}

                {resource.isPremium && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    Premium
                  </span>
                )}
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">{resource.title}</h1>
              <p className="text-lg text-gray-600 mb-6">{resource.description}</p>

              {/* Meta Information */}
              <div className="flex items-center space-x-6 text-sm text-gray-500 mb-6">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  {resource.statistics.views} views
                </div>

                {formatDuration() && (
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {formatDuration()}
                  </div>
                )}

                {resource.statistics.totalRatings > 0 && (
                  <div className="flex items-center">
                    <span className="text-yellow-400 mr-1">
                      {formatRating(resource.statistics.averageRating)}
                    </span>
                    <span>({resource.statistics.totalRatings} ratings)</span>
                  </div>
                )}
              </div>

              {/* Author */}
              <div className="flex items-center space-x-3 mb-6">
                {resource.author.profilePicture ? (
                  <img
                    src={resource.author.profilePicture}
                    alt={resource.author.name}
                    className="w-10 h-10 rounded-full"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
                    <span className="text-sm font-medium text-gray-600">
                      {resource.author.name.charAt(0)}
                    </span>
                  </div>
                )}
                <div>
                  <p className="font-medium text-gray-900">{resource.author.name}</p>
                  {resource.author.credentials && (
                    <p className="text-sm text-gray-500">{resource.author.credentials}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            {isAuthenticated && (
              <div className="flex flex-col space-y-3">
                <button
                  onClick={handleBookmark}
                  className={`flex items-center px-4 py-2 rounded-md border transition-colors ${
                    isBookmarked
                      ? 'bg-purple-50 border-purple-200 text-purple-700'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <svg className="w-4 h-4 mr-2" fill={isBookmarked ? 'currentColor' : 'none'} viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                  </svg>
                  {isBookmarked ? 'Bookmarked' : 'Bookmark'}
                </button>

                <button
                  onClick={() => setShowRatingModal(true)}
                  className="flex items-center px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                  {userRating > 0 ? `Rated ${userRating}/5` : 'Rate'}
                </button>
              </div>
            )}
          </div>

          {/* Tags */}
          {resource.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {resource.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800"
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Resource Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
          {/* Media Content */}
          {resource.type === 'video' && resource.media.videoUrl && (
            <div className="mb-8">
              <video
                controls
                className="w-full rounded-lg"
                poster={resource.media.thumbnailUrl}
              >
                <source src={resource.media.videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>
          )}

          {resource.type === 'audio' && resource.media.audioUrl && (
            <div className="mb-8">
              <audio controls className="w-full">
                <source src={resource.media.audioUrl} type="audio/mpeg" />
                Your browser does not support the audio element.
              </audio>
            </div>
          )}

          {/* Text Content */}
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: resource.content }}
          />

          {/* Download Link */}
          {resource.media.downloadUrl && (
            <div className="mt-8 p-4 bg-gray-50 rounded-lg">
              <a
                href={resource.media.downloadUrl}
                download
                className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Download Resource
              </a>
            </div>
          )}
        </div>

        {/* Similar Resources */}
        {similarResources.length > 0 && (
          <div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Similar Resources</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {similarResources.map((similarResource) => (
                <ResourceCard key={similarResource._id} resource={similarResource} />
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
