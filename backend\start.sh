#!/bin/sh

echo "🚀 Starting Theramea Backend..."

# Wait for MongoDB to be ready
echo "⏳ Waiting for MongoDB..."
while ! nc -z mongodb 27017; do
  sleep 1
done
echo "✅ MongoDB is ready"

# Wait for Redis to be ready
echo "⏳ Waiting for Redis..."
while ! nc -z redis 6379; do
  sleep 1
done
echo "✅ Redis is ready"

# Start the application
echo "🚀 Starting Node.js application..."
exec npx ts-node -r tsconfig-paths/register src/server.ts
