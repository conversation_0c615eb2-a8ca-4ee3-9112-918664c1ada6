"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuthStore } from "@/store/authStore";
import { counselorAPI } from "@/lib/counselor";
import { Counselor, COUNSELOR_SPECIALIZATIONS } from "@/types/counselor";
import Header from "@/components/layout/Header";
import CounselorList from "@/components/counselors/CounselorList";
import CounselorCard from "@/components/counselors/CounselorCard";

export default function CounselorsPage() {
  const router = useRouter();
  const { isAuthenticated, isGuest, checkAuth, isLoading, tokens } =
    useAuthStore();
  const [featuredCounselors, setFeaturedCounselors] = useState<Counselor[]>([]);
  const [featuredLoading, setFeaturedLoading] = useState(true);

  const token = tokens?.accessToken;

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isGuest && !isLoading) {
      router.push("/auth/login");
    }
  }, [isAuthenticated, isGuest, isLoading, router]);

  useEffect(() => {
    if (token) {
      fetchFeaturedCounselors();
    }
  }, [token]);

  const fetchFeaturedCounselors = async () => {
    try {
      setFeaturedLoading(true);
      const response = await counselorAPI.getFeaturedCounselors(6, token);
      setFeaturedCounselors(response.data.counselors);
    } catch (error) {
      console.error("Failed to fetch featured counselors:", error);
    } finally {
      setFeaturedLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!isAuthenticated && !isGuest) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Find Your Counselor
          </h1>
          <p className="text-gray-600">
            Connect with licensed mental health professionals who understand
            your needs.
          </p>
        </div>

        {/* How It Works */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            How It Works
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-6 h-6 text-purple-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">
                1. Browse & Filter
              </h3>
              <p className="text-sm text-gray-600">
                Search counselors by specialization, language, session type, and
                more.
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-6 h-6 text-purple-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7V3a4 4 0 118 0v4m-4 8a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">
                2. Book Session
              </h3>
              <p className="text-sm text-gray-600">
                Choose available time slots and complete secure payment.
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-6 h-6 text-purple-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">
                3. Start Session
              </h3>
              <p className="text-sm text-gray-600">
                Join secure video sessions from anywhere, anytime.
              </p>
            </div>
          </div>
        </div>

        {/* Specializations Overview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Browse by Specialization
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {COUNSELOR_SPECIALIZATIONS.slice(0, 8).map((specialization) => (
              <Link
                key={specialization.value}
                href={`/counselors?specialization=${specialization.value}`}
                className="group p-3 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all text-center"
              >
                <div className="text-2xl mb-2">{specialization.icon}</div>
                <h3 className="text-xs font-medium text-gray-900 group-hover:text-purple-600 transition-colors line-clamp-2">
                  {specialization.label}
                </h3>
              </Link>
            ))}
          </div>
          <div className="text-center mt-4">
            <Link
              href="/counselors?showAllSpecializations=true"
              className="text-purple-600 hover:text-purple-700 font-medium text-sm"
            >
              View all specializations →
            </Link>
          </div>
        </div>

        {/* Featured Counselors */}
        {featuredCounselors.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold text-gray-900">
                Featured Counselors
              </h2>
              <Link
                href="/counselors?featured=true"
                className="text-purple-600 hover:text-purple-700 font-medium"
              >
                View all →
              </Link>
            </div>

            {featuredLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div
                    key={i}
                    className="bg-white rounded-lg shadow-sm border border-gray-200 animate-pulse"
                  >
                    <div className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="w-16 h-16 bg-gray-200 rounded-full"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredCounselors.map((counselor) => (
                  <CounselorCard key={counselor._id} counselor={counselor} />
                ))}
              </div>
            )}
          </div>
        )}

        {/* Session Types */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Session Types Available
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">👤</div>
                <div>
                  <h3 className="font-medium text-gray-900">Individual</h3>
                  <p className="text-sm text-gray-600">One-on-one sessions</p>
                </div>
              </div>
            </div>

            <div className="p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">💑</div>
                <div>
                  <h3 className="font-medium text-gray-900">Couples</h3>
                  <p className="text-sm text-gray-600">
                    Relationship counseling
                  </p>
                </div>
              </div>
            </div>

            <div className="p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">👨‍👩‍👧‍👦</div>
                <div>
                  <h3 className="font-medium text-gray-900">Family</h3>
                  <p className="text-sm text-gray-600">Family therapy</p>
                </div>
              </div>
            </div>

            <div className="p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">👥</div>
                <div>
                  <h3 className="font-medium text-gray-900">Group</h3>
                  <p className="text-sm text-gray-600">Group sessions</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Safety & Privacy */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg
                className="h-6 w-6 text-blue-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-blue-900 mb-2">
                Your Privacy & Safety Matter
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-800">
                <div>
                  <h4 className="font-medium mb-1">✓ Licensed Professionals</h4>
                  <p className="text-sm">
                    All counselors are verified and licensed
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">✓ Secure Sessions</h4>
                  <p className="text-sm">End-to-end encrypted video calls</p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">✓ Confidential</h4>
                  <p className="text-sm">
                    Your sessions are completely private
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">✓ Flexible Scheduling</h4>
                  <p className="text-sm">
                    Book sessions that fit your schedule
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* All Counselors */}
        <div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            All Counselors
          </h2>
          <CounselorList />
        </div>
      </main>
    </div>
  );
}
