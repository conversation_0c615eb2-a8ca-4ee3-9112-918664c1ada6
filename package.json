{"name": "theramea", "version": "1.0.0", "description": "Theramea - Digital Counseling Platform", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "cd frontend && npm run start", "start:backend": "cd backend && npm run start", "test": "npm run test:backend && npm run test:frontend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules frontend/.next backend/dist"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["counseling", "therapy", "mental-health", "nextjs", "nodejs", "mongodb", "redis"], "author": "Theramea Team", "license": "MIT"}