// MongoDB initialization script
db = db.getSiblingDB('theramea');

// Create collections
db.createCollection('users');
db.createCollection('counselors');
db.createCollection('sessions');
db.createCollection('chatrooms');
db.createCollection('messages');
db.createCollection('resources');

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "role": 1 });
db.users.createIndex({ "isActive": 1 });

db.counselors.createIndex({ "userId": 1 }, { unique: true });
db.counselors.createIndex({ "specializations": 1 });
db.counselors.createIndex({ "verification.status": 1 });
db.counselors.createIndex({ "isActive": 1 });

db.sessions.createIndex({ "clientId": 1 });
db.sessions.createIndex({ "counselorId": 1 });
db.sessions.createIndex({ "scheduledAt": 1 });
db.sessions.createIndex({ "status": 1 });

db.chatrooms.createIndex({ "category": 1 });
db.chatrooms.createIndex({ "isPublic": 1 });
db.chatrooms.createIndex({ "isActive": 1 });

db.messages.createIndex({ "roomId": 1 });
db.messages.createIndex({ "senderId": 1 });
db.messages.createIndex({ "createdAt": 1 });

db.resources.createIndex({ "category": 1 });
db.resources.createIndex({ "type": 1 });
db.resources.createIndex({ "tags": 1 });
db.resources.createIndex({ "isPublished": 1 });

print('MongoDB initialization completed successfully!');
