import mongoose, { Document, Schema } from 'mongoose';

export interface IAdminAction extends Document {
  _id: mongoose.Types.ObjectId;
  adminId: mongoose.Types.ObjectId;
  action: string;
  targetType: 'user' | 'counselor' | 'session' | 'chatroom' | 'message' | 'resource' | 'system';
  targetId?: mongoose.Types.ObjectId;
  details: {
    description: string;
    previousState?: any;
    newState?: any;
    reason?: string;
    metadata?: any;
  };
  ipAddress: string;
  userAgent: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  createdAt: Date;
}

export interface ISystemSettings extends Document {
  _id: mongoose.Types.ObjectId;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  category: 'general' | 'security' | 'payment' | 'email' | 'chat' | 'session' | 'content';
  isPublic: boolean;
  lastModifiedBy: mongoose.Types.ObjectId;
  lastModifiedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IReport extends Document {
  _id: mongoose.Types.ObjectId;
  reportedBy: mongoose.Types.ObjectId;
  reportType: 'user' | 'counselor' | 'message' | 'session' | 'content' | 'technical';
  targetId: mongoose.Types.ObjectId;
  targetType: 'user' | 'counselor' | 'message' | 'session' | 'resource' | 'chatroom';
  reason: string;
  description: string;
  evidence: {
    screenshots?: string[];
    logs?: string[];
    additionalInfo?: any;
  };
  status: 'pending' | 'investigating' | 'resolved' | 'dismissed' | 'escalated';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignedTo?: mongoose.Types.ObjectId;
  resolution?: {
    action: string;
    description: string;
    resolvedBy: mongoose.Types.ObjectId;
    resolvedAt: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Admin Action Schema
const adminActionSchema = new Schema<IAdminAction>({
  adminId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  action: {
    type: String,
    required: true,
    enum: [
      'user_created',
      'user_updated',
      'user_suspended',
      'user_activated',
      'user_deleted',
      'counselor_approved',
      'counselor_rejected',
      'counselor_suspended',
      'session_cancelled',
      'session_refunded',
      'message_deleted',
      'message_hidden',
      'chatroom_created',
      'chatroom_closed',
      'resource_published',
      'resource_unpublished',
      'settings_updated',
      'system_maintenance',
      'backup_created',
      'security_alert'
    ]
  },
  targetType: {
    type: String,
    enum: ['user', 'counselor', 'session', 'chatroom', 'message', 'resource', 'system'],
    required: true
  },
  targetId: {
    type: Schema.Types.ObjectId
  },
  details: {
    description: {
      type: String,
      required: true,
      maxlength: [1000, 'Description cannot exceed 1000 characters']
    },
    previousState: Schema.Types.Mixed,
    newState: Schema.Types.Mixed,
    reason: String,
    metadata: Schema.Types.Mixed
  },
  ipAddress: {
    type: String,
    required: true
  },
  userAgent: {
    type: String,
    required: true
  },
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  }
}, {
  timestamps: true
});

// System Settings Schema
const systemSettingsSchema = new Schema<ISystemSettings>({
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  value: {
    type: Schema.Types.Mixed,
    required: true
  },
  type: {
    type: String,
    enum: ['string', 'number', 'boolean', 'object', 'array'],
    required: true
  },
  description: {
    type: String,
    required: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  category: {
    type: String,
    enum: ['general', 'security', 'payment', 'email', 'chat', 'session', 'content'],
    required: true
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Report Schema
const reportSchema = new Schema<IReport>({
  reportedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reportType: {
    type: String,
    enum: ['user', 'counselor', 'message', 'session', 'content', 'technical'],
    required: true
  },
  targetId: {
    type: Schema.Types.ObjectId,
    required: true
  },
  targetType: {
    type: String,
    enum: ['user', 'counselor', 'message', 'session', 'resource', 'chatroom'],
    required: true
  },
  reason: {
    type: String,
    required: true,
    enum: [
      'inappropriate_content',
      'harassment',
      'spam',
      'fake_profile',
      'unprofessional_conduct',
      'technical_issue',
      'billing_issue',
      'privacy_concern',
      'safety_concern',
      'other'
    ]
  },
  description: {
    type: String,
    required: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  evidence: {
    screenshots: [String],
    logs: [String],
    additionalInfo: Schema.Types.Mixed
  },
  status: {
    type: String,
    enum: ['pending', 'investigating', 'resolved', 'dismissed', 'escalated'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  resolution: {
    action: String,
    description: String,
    resolvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    resolvedAt: Date
  }
}, {
  timestamps: true
});

// Indexes for Admin Actions
adminActionSchema.index({ adminId: 1, createdAt: -1 });
adminActionSchema.index({ action: 1, createdAt: -1 });
adminActionSchema.index({ targetType: 1, targetId: 1 });
adminActionSchema.index({ severity: 1, createdAt: -1 });

// Indexes for System Settings
systemSettingsSchema.index({ key: 1 });
systemSettingsSchema.index({ category: 1 });
systemSettingsSchema.index({ isPublic: 1 });

// Indexes for Reports
reportSchema.index({ reportedBy: 1, createdAt: -1 });
reportSchema.index({ status: 1, priority: 1 });
reportSchema.index({ targetType: 1, targetId: 1 });
reportSchema.index({ assignedTo: 1, status: 1 });
reportSchema.index({ createdAt: -1 });

// Virtual to populate admin details
adminActionSchema.virtual('admin', {
  ref: 'User',
  localField: 'adminId',
  foreignField: '_id',
  justOne: true
});

// Virtual to populate reporter details
reportSchema.virtual('reporter', {
  ref: 'User',
  localField: 'reportedBy',
  foreignField: '_id',
  justOne: true
});

// Virtual to populate assigned admin details
reportSchema.virtual('assignedAdmin', {
  ref: 'User',
  localField: 'assignedTo',
  foreignField: '_id',
  justOne: true
});

export const AdminAction = mongoose.model<IAdminAction>('AdminAction', adminActionSchema);
export const SystemSettings = mongoose.model<ISystemSettings>('SystemSettings', systemSettingsSchema);
export const Report = mongoose.model<IReport>('Report', reportSchema);
