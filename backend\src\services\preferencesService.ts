import { User, IUser } from '@/models/User';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

export interface NotificationPreferences {
  email?: boolean;
  push?: boolean;
  sessionReminders?: boolean;
  chatMessages?: boolean;
}

export interface PrivacyPreferences {
  showOnlineStatus?: boolean;
  allowDirectMessages?: boolean;
}

export interface UserPreferences {
  notifications?: NotificationPreferences;
  privacy?: PrivacyPreferences;
}

export class PreferencesService {
  /**
   * Get user preferences
   */
  static async getUserPreferences(userId: string): Promise<UserPreferences> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      return user.preferences;
    } catch (error) {
      logger.error('Get user preferences error:', error);
      throw error;
    }
  }

  /**
   * Update notification preferences
   */
  static async updateNotificationPreferences(
    userId: string, 
    preferences: NotificationPreferences
  ): Promise<UserPreferences> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      // Update notification preferences
      user.preferences.notifications = {
        ...user.preferences.notifications,
        ...preferences
      };

      await user.save();
      logger.info(`Notification preferences updated for user: ${user.email}`);

      return user.preferences;
    } catch (error) {
      logger.error('Update notification preferences error:', error);
      throw error;
    }
  }

  /**
   * Update privacy preferences
   */
  static async updatePrivacyPreferences(
    userId: string, 
    preferences: PrivacyPreferences
  ): Promise<UserPreferences> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      // Update privacy preferences
      user.preferences.privacy = {
        ...user.preferences.privacy,
        ...preferences
      };

      await user.save();
      logger.info(`Privacy preferences updated for user: ${user.email}`);

      return user.preferences;
    } catch (error) {
      logger.error('Update privacy preferences error:', error);
      throw error;
    }
  }

  /**
   * Update all preferences at once
   */
  static async updateAllPreferences(
    userId: string, 
    preferences: UserPreferences
  ): Promise<UserPreferences> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      // Update all preferences
      if (preferences.notifications) {
        user.preferences.notifications = {
          ...user.preferences.notifications,
          ...preferences.notifications
        };
      }

      if (preferences.privacy) {
        user.preferences.privacy = {
          ...user.preferences.privacy,
          ...preferences.privacy
        };
      }

      await user.save();
      logger.info(`All preferences updated for user: ${user.email}`);

      return user.preferences;
    } catch (error) {
      logger.error('Update all preferences error:', error);
      throw error;
    }
  }

  /**
   * Reset preferences to default
   */
  static async resetPreferences(userId: string): Promise<UserPreferences> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      // Reset to default preferences
      user.preferences = {
        notifications: {
          email: true,
          push: true,
          sessionReminders: true,
          chatMessages: true
        },
        privacy: {
          showOnlineStatus: true,
          allowDirectMessages: true
        }
      };

      await user.save();
      logger.info(`Preferences reset to default for user: ${user.email}`);

      return user.preferences;
    } catch (error) {
      logger.error('Reset preferences error:', error);
      throw error;
    }
  }

  /**
   * Get notification preferences for multiple users (for sending notifications)
   */
  static async getNotificationPreferencesForUsers(userIds: string[]): Promise<Map<string, NotificationPreferences>> {
    try {
      const users = await User.find({ 
        _id: { $in: userIds },
        isActive: true 
      }).select('preferences.notifications');

      const preferencesMap = new Map<string, NotificationPreferences>();
      
      users.forEach(user => {
        preferencesMap.set(user._id.toString(), user.preferences.notifications);
      });

      return preferencesMap;
    } catch (error) {
      logger.error('Get notification preferences for users error:', error);
      throw error;
    }
  }

  /**
   * Check if user allows specific notification type
   */
  static async canSendNotification(
    userId: string, 
    notificationType: keyof NotificationPreferences
  ): Promise<boolean> {
    try {
      const user = await User.findById(userId).select('preferences.notifications isActive');
      
      if (!user || !user.isActive) {
        return false;
      }

      return user.preferences.notifications[notificationType] ?? true;
    } catch (error) {
      logger.error('Check notification permission error:', error);
      return false;
    }
  }

  /**
   * Bulk update notification preferences for admin operations
   */
  static async bulkUpdateNotificationPreferences(
    userIds: string[],
    preferences: NotificationPreferences
  ): Promise<{ updated: number; failed: string[] }> {
    try {
      const result = await User.updateMany(
        { _id: { $in: userIds } },
        { 
          $set: {
            'preferences.notifications': preferences
          }
        }
      );

      logger.info(`Bulk updated notification preferences for ${result.modifiedCount} users`);

      return {
        updated: result.modifiedCount,
        failed: [] // In a real implementation, you might track failed updates
      };
    } catch (error) {
      logger.error('Bulk update notification preferences error:', error);
      throw error;
    }
  }

  /**
   * Get users with specific notification preferences enabled
   */
  static async getUsersWithNotificationEnabled(
    notificationType: keyof NotificationPreferences,
    additionalFilters: any = {}
  ): Promise<IUser[]> {
    try {
      const filter = {
        [`preferences.notifications.${notificationType}`]: true,
        isActive: true,
        ...additionalFilters
      };

      const users = await User.find(filter);
      return users;
    } catch (error) {
      logger.error('Get users with notification enabled error:', error);
      throw error;
    }
  }

  /**
   * Export user preferences (for data export/GDPR compliance)
   */
  static async exportUserPreferences(userId: string): Promise<UserPreferences> {
    try {
      const user = await User.findById(userId).select('preferences');
      
      if (!user) {
        throw createError('User not found', 404);
      }

      return user.preferences;
    } catch (error) {
      logger.error('Export user preferences error:', error);
      throw error;
    }
  }

  /**
   * Import user preferences (for data import/migration)
   */
  static async importUserPreferences(
    userId: string, 
    preferences: UserPreferences
  ): Promise<UserPreferences> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      // Validate and import preferences
      user.preferences = {
        notifications: {
          email: preferences.notifications?.email ?? true,
          push: preferences.notifications?.push ?? true,
          sessionReminders: preferences.notifications?.sessionReminders ?? true,
          chatMessages: preferences.notifications?.chatMessages ?? true
        },
        privacy: {
          showOnlineStatus: preferences.privacy?.showOnlineStatus ?? true,
          allowDirectMessages: preferences.privacy?.allowDirectMessages ?? true
        }
      };

      await user.save();
      logger.info(`Preferences imported for user: ${user.email}`);

      return user.preferences;
    } catch (error) {
      logger.error('Import user preferences error:', error);
      throw error;
    }
  }
}
