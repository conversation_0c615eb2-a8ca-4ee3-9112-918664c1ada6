// Chat API functions
import { Chat<PERSON><PERSON>, Chat<PERSON>essage, ChatRoomFilters, ChatRoomListOptions, SendMessageData } from '@/types/chat';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

class ChatAPI {
  private getHeaders(token?: string) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }

  async getChatRooms(
    filters: ChatRoomFilters = {},
    options: ChatRoomListOptions = {},
    token?: string
  ): Promise<{
    success: boolean;
    data: {
      chatRooms: ChatRoom[];
      pagination: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    
    if (options.page) queryParams.append('page', options.page.toString());
    if (options.limit) queryParams.append('limit', options.limit.toString());
    if (options.sortBy) queryParams.append('sortBy', options.sortBy);
    if (options.sortOrder) queryParams.append('sortOrder', options.sortOrder);
    
    if (filters.topic) queryParams.append('topic', filters.topic);
    if (filters.category) queryParams.append('category', filters.category);
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.tags) {
      filters.tags.forEach(tag => queryParams.append('tags', tag));
    }

    const response = await fetch(`${API_BASE_URL}/chat/rooms?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch chat rooms');
    }

    return response.json();
  }

  async getChatRoom(roomId: string, token?: string): Promise<{
    success: boolean;
    data: { chatRoom: ChatRoom };
  }> {
    const response = await fetch(`${API_BASE_URL}/chat/rooms/${roomId}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch chat room');
    }

    return response.json();
  }

  async getChatMessages(
    roomId: string,
    page: number = 1,
    limit: number = 50,
    token?: string
  ): Promise<{
    success: boolean;
    data: {
      messages: ChatMessage[];
      pagination: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await fetch(`${API_BASE_URL}/chat/rooms/${roomId}/messages?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch messages');
    }

    return response.json();
  }

  async sendMessage(
    roomId: string,
    messageData: SendMessageData,
    token?: string
  ): Promise<{
    success: boolean;
    data: { message: ChatMessage };
  }> {
    const response = await fetch(`${API_BASE_URL}/chat/rooms/${roomId}/messages`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify(messageData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to send message');
    }

    return response.json();
  }

  async joinChatRoom(
    roomId: string,
    displayName: string,
    token?: string
  ): Promise<{
    success: boolean;
    data: { chatRoom: ChatRoom };
  }> {
    const response = await fetch(`${API_BASE_URL}/chat/rooms/${roomId}/join`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify({ displayName }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to join chat room');
    }

    return response.json();
  }

  async leaveChatRoom(
    roomId: string,
    token?: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/chat/rooms/${roomId}/leave`, {
      method: 'POST',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to leave chat room');
    }

    return response.json();
  }

  async reportMessage(
    messageId: string,
    reason: 'spam' | 'harassment' | 'inappropriate' | 'off-topic' | 'other',
    description?: string,
    token?: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/chat/report`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify({
        messageId,
        reason,
        description,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to report message');
    }

    return response.json();
  }

  async uploadChatFile(
    file: File,
    token?: string
  ): Promise<{
    success: boolean;
    data: {
      fileUrl: string;
      fileName: string;
      fileSize: number;
      mimeType: string;
    };
  }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/chat/upload`, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to upload file');
    }

    return response.json();
  }

  // Moderator actions (require appropriate permissions)
  async hideMessage(
    messageId: string,
    reason: string,
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/chat/messages/${messageId}/hide`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to hide message');
    }

    return response.json();
  }

  async deleteMessage(
    messageId: string,
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/chat/messages/${messageId}`, {
      method: 'DELETE',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete message');
    }

    return response.json();
  }

  async muteUser(
    roomId: string,
    userId: string,
    duration: number, // in minutes
    reason: string,
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/chat/rooms/${roomId}/users/${userId}/mute`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ duration, reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to mute user');
    }

    return response.json();
  }
}

export const chatAPI = new ChatAPI();
