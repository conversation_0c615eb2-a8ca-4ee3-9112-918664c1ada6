'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import { counselorPortalAPI } from '@/lib/counselorPortal';
import CounselorLayout from '@/components/counselor/CounselorLayout';
import CounselorDashboard from '@/components/counselor/CounselorDashboard';

export default function CounselorPortalPage() {
  const router = useRouter();
  const { isAuthenticated, user, checkAuth, isLoading, tokens } = useAuthStore();
  const [counselorExists, setCounselorExists] = useState<boolean | null>(null);
  const [checkingCounselor, setCheckingCounselor] = useState(true);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      router.push('/auth/login');
      return;
    }

    if (isAuthenticated && user?.role !== 'counselor') {
      router.push('/dashboard');
      return;
    }

    if (isAuthenticated && user?.role === 'counselor' && tokens?.accessToken) {
      checkCounselorProfile();
    }
  }, [isAuthenticated, user, isLoading, router, tokens]);

  const checkCounselorProfile = async () => {
    try {
      setCheckingCounselor(true);
      await counselorPortalAPI.getMyProfile(tokens!.accessToken);
      setCounselorExists(true);
    } catch (error) {
      // If profile doesn't exist, redirect to registration
      setCounselorExists(false);
      router.push('/counselor/register');
    } finally {
      setCheckingCounselor(false);
    }
  };

  if (isLoading || checkingCounselor) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  if (user?.role !== 'counselor') {
    return null; // Will redirect
  }

  if (counselorExists === false) {
    return null; // Will redirect to registration
  }

  return (
    <CounselorLayout>
      <CounselorDashboard />
    </CounselorLayout>
  );
}
