import { Router } from "express";
import passport from "@/config/passport";
import { AuthController } from "@/controllers/authController";
import { userValidations } from "@/utils/validation";
import {
  authenticate,
  optionalAuth,
  authRateLimit,
  trackLoginAttempt,
} from "@/middleware/auth";

const router = Router();

// Public routes
router.post(
  "/register",
  authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  userValidations.register,
  AuthController.register
);

router.post(
  "/login",
  authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  trackLoginAttempt,
  userValidations.login,
  AuthController.login
);

router.post("/refresh-token", AuthController.refreshToken);

router.get("/verify-email", AuthController.verifyEmail);

router.post(
  "/resend-verification",
  authRateLimit(3, 60 * 60 * 1000), // 3 attempts per hour
  AuthController.resendVerificationEmail
);

router.post(
  "/forgot-password",
  authRateLimit(3, 60 * 60 * 1000), // 3 attempts per hour
  AuthController.requestPasswordReset
);

router.post(
  "/reset-password",
  authRateLimit(5, 60 * 60 * 1000), // 5 attempts per hour
  AuthController.resetPassword
);

// Guest access
router.post("/guest-token", AuthController.generateGuestToken);

// Google OAuth routes
router.get(
  "/google",
  passport.authenticate("google", { scope: ["profile", "email"] })
);

router.get(
  "/google/callback",
  passport.authenticate("google", {
    failureRedirect: `${process.env.FRONTEND_URL}/login?error=oauth_failed`,
  }),
  AuthController.googleAuthCallback
);

// Protected routes
router.post("/logout", authenticate, AuthController.logout);

router.post("/change-password", authenticate, AuthController.changePassword);

router.get("/profile", authenticate, AuthController.getProfile);

router.get("/check", optionalAuth, AuthController.checkAuth);

export default router;
