'use client';

import { useAuthStore } from '@/store/authStore';

export default function WelcomeWidget() {
  const { user, isGuest } = useAuthStore();

  return (
    <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-white">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold mb-2">
            {isGuest ? 'Welcome to Theramea! 👋' : `Welcome back, ${user?.firstName}! 👋`}
          </h2>
          <p className="text-purple-100">
            {isGuest 
              ? 'Explore our features and discover how we can support your mental health journey.'
              : 'How are you feeling today? We\'re here to support you every step of the way.'
            }
          </p>
        </div>
        <div className="hidden sm:block">
          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
}
