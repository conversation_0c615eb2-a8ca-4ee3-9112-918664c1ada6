// Counselor and booking API functions
import { Counselor, Session, CounselorFilters, CounselorListOptions, BookingRequest, AvailabilitySlot } from '@/types/counselor';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

class CounselorAPI {
  private getHeaders(token?: string) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }

  async getCounselors(
    filters: CounselorFilters = {},
    options: CounselorListOptions = {},
    token?: string
  ): Promise<{
    success: boolean;
    data: {
      counselors: Counselor[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    
    if (options.page) queryParams.append('page', options.page.toString());
    if (options.limit) queryParams.append('limit', options.limit.toString());
    if (options.sortBy) queryParams.append('sortBy', options.sortBy);
    if (options.sortOrder) queryParams.append('sortOrder', options.sortOrder);
    
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.minRating) queryParams.append('minRating', filters.minRating.toString());
    if (filters.maxRate) queryParams.append('maxRate', filters.maxRate.toString());
    if (filters.currency) queryParams.append('currency', filters.currency);
    
    if (filters.specializations) {
      filters.specializations.forEach(spec => queryParams.append('specializations', spec));
    }
    
    if (filters.languages) {
      filters.languages.forEach(lang => queryParams.append('languages', lang));
    }
    
    if (filters.sessionTypes) {
      filters.sessionTypes.forEach(type => queryParams.append('sessionTypes', type));
    }

    const response = await fetch(`${API_BASE_URL}/counselors?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch counselors');
    }

    return response.json();
  }

  async getCounselor(counselorId: string, token?: string): Promise<{
    success: boolean;
    data: { counselor: Counselor };
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/${counselorId}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch counselor');
    }

    return response.json();
  }

  async getCounselorAvailability(
    counselorId: string,
    startDate: string,
    endDate: string,
    duration: number = 60,
    timezone: string = 'Africa/Lagos',
    token?: string
  ): Promise<{
    success: boolean;
    data: { slots: AvailabilitySlot[] };
  }> {
    const queryParams = new URLSearchParams({
      startDate,
      endDate,
      duration: duration.toString(),
      timezone,
    });

    const response = await fetch(`${API_BASE_URL}/counselors/${counselorId}/availability?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch availability');
    }

    return response.json();
  }

  async getFeaturedCounselors(limit: number = 6, token?: string): Promise<{
    success: boolean;
    data: { counselors: Counselor[] };
  }> {
    const queryParams = new URLSearchParams({
      featured: 'true',
      limit: limit.toString(),
      sortBy: 'statistics.averageRating',
      sortOrder: 'desc',
    });

    const response = await fetch(`${API_BASE_URL}/counselors?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch featured counselors');
    }

    return response.json();
  }

  async searchCounselors(query: string, filters: CounselorFilters = {}, token?: string): Promise<{
    success: boolean;
    data: {
      results: Counselor[];
      suggestions: string[];
      totalResults: number;
    };
  }> {
    const queryParams = new URLSearchParams({ q: query });
    
    if (filters.specializations) {
      filters.specializations.forEach(spec => queryParams.append('specializations', spec));
    }

    const response = await fetch(`${API_BASE_URL}/counselors/search?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Search failed');
    }

    return response.json();
  }
}

class BookingAPI {
  private getHeaders(token?: string) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }

  async createBooking(bookingData: BookingRequest, token: string): Promise<{
    success: boolean;
    message: string;
    data: { session: Session };
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify(bookingData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create booking');
    }

    return response.json();
  }

  async getBookings(token: string, status?: string): Promise<{
    success: boolean;
    data: { sessions: Session[] };
  }> {
    const queryParams = new URLSearchParams();
    if (status) queryParams.append('status', status);

    const response = await fetch(`${API_BASE_URL}/sessions?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch bookings');
    }

    return response.json();
  }

  async getBooking(sessionId: string, token: string): Promise<{
    success: boolean;
    data: { session: Session };
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch booking');
    }

    return response.json();
  }

  async cancelBooking(sessionId: string, reason: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/cancel`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to cancel booking');
    }

    return response.json();
  }

  async rescheduleBooking(sessionId: string, newDateTime: string, token: string): Promise<{
    success: boolean;
    message: string;
    data: { session: Session };
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/reschedule`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ newDateTime }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to reschedule booking');
    }

    return response.json();
  }

  async initializePayment(sessionId: string, token: string): Promise<{
    success: boolean;
    data: {
      paymentUrl: string;
      reference: string;
      amount: number;
      currency: string;
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/payment/initialize`, {
      method: 'POST',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to initialize payment');
    }

    return response.json();
  }

  async verifyPayment(sessionId: string, reference: string, token: string): Promise<{
    success: boolean;
    message: string;
    data: { session: Session };
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/payment/verify`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify({ reference }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to verify payment');
    }

    return response.json();
  }

  async calculatePricing(counselorId: string, duration: number, sessionType: string): Promise<{
    success: boolean;
    data: {
      baseAmount: number;
      platformFee: number;
      totalAmount: number;
      currency: string;
      counselorEarnings: number;
    };
  }> {
    const queryParams = new URLSearchParams({
      counselorId,
      duration: duration.toString(),
      sessionType,
    });

    const response = await fetch(`${API_BASE_URL}/sessions/pricing?${queryParams}`, {
      method: 'GET',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to calculate pricing');
    }

    return response.json();
  }

  async submitFeedback(sessionId: string, feedback: {
    rating: number;
    comment?: string;
    wouldRecommend: boolean;
  }, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/feedback`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify(feedback),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to submit feedback');
    }

    return response.json();
  }
}

export const counselorAPI = new CounselorAPI();
export const bookingAPI = new BookingAPI();
