import { Counselor, I<PERSON>ounselor } from '@/models/Counselor';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

export interface CounselorStats {
  overview: {
    totalSessions: number;
    totalEarnings: number;
    averageRating: number;
    totalReviews: number;
    completionRate: number;
    responseTime: number;
  };
  monthlyStats: {
    month: string;
    sessions: number;
    earnings: number;
    newClients: number;
  }[];
  ratingDistribution: {
    rating: number;
    count: number;
  }[];
  topSpecializations: {
    specialization: string;
    sessionCount: number;
  }[];
  recentActivity: {
    date: string;
    type: 'session' | 'review' | 'booking';
    description: string;
  }[];
}

export interface PlatformCounselorStats {
  totalCounselors: number;
  approvedCounselors: number;
  pendingApplications: number;
  rejectedApplications: number;
  activeCounselors: number; // counselors with sessions in last 30 days
  averageRating: number;
  topSpecializations: {
    specialization: string;
    counselorCount: number;
  }[];
  monthlyApplications: {
    month: string;
    applications: number;
    approvals: number;
  }[];
}

export class CounselorStatsService {
  /**
   * Get comprehensive stats for a specific counselor
   */
  static async getCounselorStats(counselorId: string): Promise<CounselorStats> {
    try {
      const counselor = await Counselor.findById(counselorId);
      if (!counselor) {
        throw createError('Counselor not found', 404);
      }

      // TODO: When Session model is integrated, fetch real session data
      // For now, return the stored statistics
      const overview = {
        totalSessions: counselor.statistics.totalSessions,
        totalEarnings: counselor.statistics.totalEarnings,
        averageRating: counselor.statistics.averageRating,
        totalReviews: counselor.statistics.totalReviews,
        completionRate: counselor.statistics.completionRate,
        responseTime: counselor.statistics.responseTime
      };

      // Mock data for demonstration - replace with real queries when Session model is available
      const monthlyStats = this.generateMockMonthlyStats();
      const ratingDistribution = this.generateMockRatingDistribution();
      const topSpecializations = counselor.specializations.map((spec, index) => ({
        specialization: spec,
        sessionCount: Math.floor(Math.random() * 20) + 1
      }));
      const recentActivity = this.generateMockRecentActivity();

      return {
        overview,
        monthlyStats,
        ratingDistribution,
        topSpecializations,
        recentActivity
      };
    } catch (error) {
      logger.error('Get counselor stats error:', error);
      throw error;
    }
  }

  /**
   * Update counselor statistics after a session
   */
  static async updateSessionStats(
    counselorId: string,
    sessionData: {
      earnings: number;
      rating?: number;
      completed: boolean;
    }
  ): Promise<void> {
    try {
      const counselor = await Counselor.findById(counselorId);
      if (!counselor) {
        throw createError('Counselor not found', 404);
      }

      // Update session count
      counselor.statistics.totalSessions += 1;

      // Update earnings
      counselor.statistics.totalEarnings += sessionData.earnings;

      // Update completion rate
      if (sessionData.completed) {
        const completedSessions = Math.floor(
          (counselor.statistics.completionRate / 100) * (counselor.statistics.totalSessions - 1)
        ) + 1;
        counselor.statistics.completionRate = (completedSessions / counselor.statistics.totalSessions) * 100;
      } else {
        const completedSessions = Math.floor(
          (counselor.statistics.completionRate / 100) * (counselor.statistics.totalSessions - 1)
        );
        counselor.statistics.completionRate = (completedSessions / counselor.statistics.totalSessions) * 100;
      }

      // Update rating if provided
      if (sessionData.rating) {
        const totalRatingPoints = counselor.statistics.averageRating * counselor.statistics.totalReviews;
        counselor.statistics.totalReviews += 1;
        counselor.statistics.averageRating = (totalRatingPoints + sessionData.rating) / counselor.statistics.totalReviews;
      }

      await counselor.save();
      logger.info(`Statistics updated for counselor: ${counselorId}`);
    } catch (error) {
      logger.error('Update session stats error:', error);
      throw error;
    }
  }

  /**
   * Get platform-wide counselor statistics (admin only)
   */
  static async getPlatformCounselorStats(): Promise<PlatformCounselorStats> {
    try {
      const [
        totalCounselors,
        approvedCounselors,
        pendingApplications,
        rejectedApplications,
        averageRatingResult,
        specializationStats
      ] = await Promise.all([
        Counselor.countDocuments(),
        Counselor.countDocuments({ 'verification.status': 'approved' }),
        Counselor.countDocuments({ 'verification.status': 'pending' }),
        Counselor.countDocuments({ 'verification.status': 'rejected' }),
        Counselor.aggregate([
          { $match: { 'verification.status': 'approved' } },
          { $group: { _id: null, avgRating: { $avg: '$statistics.averageRating' } } }
        ]),
        Counselor.aggregate([
          { $unwind: '$specializations' },
          { $group: { _id: '$specializations', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ])
      ]);

      const topSpecializations = specializationStats.map((item: any) => ({
        specialization: item._id,
        counselorCount: item.count
      }));

      const averageRating = averageRatingResult[0]?.avgRating || 0;

      // TODO: Calculate active counselors based on recent sessions
      const activeCounselors = Math.floor(approvedCounselors * 0.7); // Mock calculation

      // Mock monthly applications data
      const monthlyApplications = this.generateMockMonthlyApplications();

      return {
        totalCounselors,
        approvedCounselors,
        pendingApplications,
        rejectedApplications,
        activeCounselors,
        averageRating,
        topSpecializations,
        monthlyApplications
      };
    } catch (error) {
      logger.error('Get platform counselor stats error:', error);
      throw error;
    }
  }

  /**
   * Get counselor leaderboard (top performers)
   */
  static async getCounselorLeaderboard(limit: number = 10): Promise<any[]> {
    try {
      const topCounselors = await Counselor.find({ 'verification.status': 'approved' })
        .populate('userId', 'firstName lastName profilePicture')
        .sort({ 'statistics.averageRating': -1, 'statistics.totalSessions': -1 })
        .limit(limit)
        .select('userId statistics specializations');

      return topCounselors.map((counselor, index) => ({
        rank: index + 1,
        counselor: {
          id: counselor._id,
          name: `${(counselor.userId as any).firstName} ${(counselor.userId as any).lastName}`,
          profilePicture: (counselor.userId as any).profilePicture,
          specializations: counselor.specializations.slice(0, 3) // Top 3 specializations
        },
        stats: {
          averageRating: counselor.statistics.averageRating,
          totalSessions: counselor.statistics.totalSessions,
          totalReviews: counselor.statistics.totalReviews
        }
      }));
    } catch (error) {
      logger.error('Get counselor leaderboard error:', error);
      throw error;
    }
  }

  /**
   * Get counselor performance metrics for admin review
   */
  static async getCounselorPerformanceMetrics(counselorId: string): Promise<any> {
    try {
      const counselor = await Counselor.findById(counselorId).populate('userId', 'firstName lastName email');
      if (!counselor) {
        throw createError('Counselor not found', 404);
      }

      // Calculate performance score (0-100)
      const performanceScore = this.calculatePerformanceScore(counselor);

      return {
        counselor: {
          id: counselor._id,
          name: `${(counselor.userId as any).firstName} ${(counselor.userId as any).lastName}`,
          email: (counselor.userId as any).email,
          verificationStatus: counselor.verification.status,
          joinedDate: counselor.createdAt
        },
        performance: {
          score: performanceScore,
          rating: counselor.statistics.averageRating,
          totalSessions: counselor.statistics.totalSessions,
          completionRate: counselor.statistics.completionRate,
          responseTime: counselor.statistics.responseTime,
          totalEarnings: counselor.statistics.totalEarnings
        },
        flags: this.generatePerformanceFlags(counselor)
      };
    } catch (error) {
      logger.error('Get counselor performance metrics error:', error);
      throw error;
    }
  }

  /**
   * Calculate performance score based on various metrics
   */
  private static calculatePerformanceScore(counselor: ICounselor): number {
    let score = 0;

    // Rating component (40% of score)
    score += (counselor.statistics.averageRating / 5) * 40;

    // Completion rate component (30% of score)
    score += (counselor.statistics.completionRate / 100) * 30;

    // Session volume component (20% of score)
    const sessionScore = Math.min(counselor.statistics.totalSessions / 50, 1) * 20;
    score += sessionScore;

    // Response time component (10% of score)
    const responseScore = Math.max(0, (60 - counselor.statistics.responseTime) / 60) * 10;
    score += responseScore;

    return Math.round(score);
  }

  /**
   * Generate performance flags for admin review
   */
  private static generatePerformanceFlags(counselor: ICounselor): string[] {
    const flags: string[] = [];

    if (counselor.statistics.averageRating < 3.5) {
      flags.push('Low rating');
    }

    if (counselor.statistics.completionRate < 80) {
      flags.push('Low completion rate');
    }

    if (counselor.statistics.responseTime > 120) {
      flags.push('Slow response time');
    }

    if (counselor.statistics.totalSessions < 5 && 
        new Date().getTime() - counselor.createdAt.getTime() > 30 * 24 * 60 * 60 * 1000) {
      flags.push('Low activity');
    }

    return flags;
  }

  // Mock data generators (replace with real data when Session model is integrated)
  private static generateMockMonthlyStats() {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map(month => ({
      month,
      sessions: Math.floor(Math.random() * 20) + 5,
      earnings: Math.floor(Math.random() * 5000) + 1000,
      newClients: Math.floor(Math.random() * 10) + 2
    }));
  }

  private static generateMockRatingDistribution() {
    return [
      { rating: 5, count: 45 },
      { rating: 4, count: 25 },
      { rating: 3, count: 8 },
      { rating: 2, count: 2 },
      { rating: 1, count: 1 }
    ];
  }

  private static generateMockRecentActivity() {
    return [
      { date: '2024-01-15', type: 'session' as const, description: 'Completed session with John D.' },
      { date: '2024-01-14', type: 'review' as const, description: 'Received 5-star review from Sarah M.' },
      { date: '2024-01-13', type: 'booking' as const, description: 'New booking from Mike R.' }
    ];
  }

  private static generateMockMonthlyApplications() {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map(month => ({
      month,
      applications: Math.floor(Math.random() * 15) + 5,
      approvals: Math.floor(Math.random() * 10) + 3
    }));
  }
}
