import multer from "multer";
import { Request } from "express";
import { createError } from "@/middleware/errorHandler";
import { FILE_TYPES, FILE_SIZE_LIMITS, validateFile } from "@/utils/cloudinary";

// Memory storage for Cloudinary upload
const storage = multer.memoryStorage();

// File filter function
const createFileFilter = (allowedTypes: string[]) => {
  return (
    req: Request,
    file: Express.Multer.File,
    cb: (error: Error | null, acceptFile?: boolean) => void
  ) => {
    try {
      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(
          createError(
            `Invalid file type. Allowed types: ${allowedTypes.join(", ")}`,
            400
          )
        );
      }
    } catch (error) {
      cb(error instanceof Error ? error : new Error("File filter error"));
    }
  };
};

// Profile picture upload middleware
export const uploadProfilePicture = multer({
  storage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.PROFILE_PICTURE,
    files: 1,
  },
  fileFilter: createFileFilter(FILE_TYPES.IMAGES),
}).single("profilePicture");

// Resource image upload middleware
export const uploadResourceImage = multer({
  storage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.RESOURCE_IMAGE,
    files: 1,
  },
  fileFilter: createFileFilter(FILE_TYPES.IMAGES),
}).single("image");

// Resource video upload middleware
export const uploadResourceVideo = multer({
  storage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.RESOURCE_VIDEO,
    files: 1,
  },
  fileFilter: createFileFilter(FILE_TYPES.VIDEOS),
}).single("video");

// Document upload middleware
export const uploadDocument = multer({
  storage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.DOCUMENT,
    files: 1,
  },
  fileFilter: createFileFilter(FILE_TYPES.DOCUMENTS),
}).single("document");

// Audio upload middleware
export const uploadAudio = multer({
  storage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.AUDIO,
    files: 1,
  },
  fileFilter: createFileFilter(FILE_TYPES.AUDIO),
}).single("audio");

// Multiple files upload middleware
export const uploadMultipleFiles = multer({
  storage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.DOCUMENT,
    files: 5, // Maximum 5 files
  },
  fileFilter: createFileFilter([...FILE_TYPES.IMAGES, ...FILE_TYPES.DOCUMENTS]),
}).array("files", 5);

// Counselor verification documents upload
export const uploadVerificationDocuments = multer({
  storage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.DOCUMENT,
    files: 10, // ID + certificates
  },
  fileFilter: createFileFilter([...FILE_TYPES.IMAGES, ...FILE_TYPES.DOCUMENTS]),
}).fields([
  { name: "idDocument", maxCount: 1 },
  { name: "certificates", maxCount: 9 },
]);

// Chat file upload middleware (for file sharing in chat)
export const uploadChatFile = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB for chat files
    files: 1,
  },
  fileFilter: createFileFilter([
    ...FILE_TYPES.IMAGES,
    ...FILE_TYPES.DOCUMENTS,
    ...FILE_TYPES.AUDIO,
  ]),
}).single("file");

// Generic file upload with custom options
export const createUploadMiddleware = (options: {
  fieldName: string;
  allowedTypes: string[];
  maxSize: number;
  maxFiles?: number;
}) => {
  const { fieldName, allowedTypes, maxSize, maxFiles = 1 } = options;

  return multer({
    storage,
    limits: {
      fileSize: maxSize,
      files: maxFiles,
    },
    fileFilter: createFileFilter(allowedTypes),
  }).single(fieldName);
};

// Error handling middleware for multer errors
export const handleUploadError = (
  error: any,
  req: Request,
  res: any,
  next: any
) => {
  if (error instanceof multer.MulterError) {
    switch (error.code) {
      case "LIMIT_FILE_SIZE":
        return next(createError("File size too large", 400));
      case "LIMIT_FILE_COUNT":
        return next(createError("Too many files uploaded", 400));
      case "LIMIT_UNEXPECTED_FILE":
        return next(createError("Unexpected file field", 400));
      default:
        return next(createError("File upload error", 400));
    }
  }
  next(error);
};
