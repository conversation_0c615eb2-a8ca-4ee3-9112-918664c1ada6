# Use Node.js 18 LTS as base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies including curl for health check
RUN apk add --no-cache libc6-compat curl

# Copy package files first for better caching
COPY package*.json ./
COPY postcss.config.js ./
COPY tailwind.config.js ./
COPY next.config.js* ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Create necessary directories with proper permissions
RUN mkdir -p .next uploads && \
    chown -R node:node /app

# Switch to node user
USER node

# Expose port
EXPOSE 3000

# Set environment for Next.js
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV development

# Health check - wait longer for Next.js to start
HEALTHCHECK --interval=30s --timeout=15s --start-period=90s --retries=5 \
  CMD curl -f http://localhost:3000 || exit 1

# Start the application with host binding
CMD ["npm", "run", "dev", "--", "--hostname", "0.0.0.0"]
