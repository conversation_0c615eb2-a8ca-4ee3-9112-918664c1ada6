// Video session API functions
import { MeetingToken, SessionRecording, SessionFeedback, SessionAnalytics, ConnectionIssue } from '@/types/video';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

class VideoAPI {
  private getHeaders(token?: string) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }

  // Session Management
  async startSession(sessionId: string, token: string): Promise<{
    success: boolean;
    message: string;
    data: {
      session: any;
      roomUrl: string;
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/start`, {
      method: 'POST',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to start session');
    }

    return response.json();
  }

  async endSession(sessionId: string, duration: number, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/end`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ duration }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to end session');
    }

    return response.json();
  }

  async markNoShow(sessionId: string, reason: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/no-show`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to mark no-show');
    }

    return response.json();
  }

  // Room Management
  async createRoom(sessionId: string, token: string): Promise<{
    success: boolean;
    message: string;
    data: {
      room: {
        id: string;
        name: string;
        url: string;
      };
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/room`, {
      method: 'POST',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create room');
    }

    return response.json();
  }

  async generateToken(sessionId: string, token: string): Promise<{
    success: boolean;
    message: string;
    data: {
      token: MeetingToken;
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/token`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to generate token');
    }

    return response.json();
  }

  // Recording Management
  async startRecording(sessionId: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/recording/start`, {
      method: 'POST',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to start recording');
    }

    return response.json();
  }

  async stopRecording(sessionId: string, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/recording/stop`, {
      method: 'POST',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to stop recording');
    }

    return response.json();
  }

  async getRecordings(sessionId: string, token: string): Promise<{
    success: boolean;
    data: {
      recordings: SessionRecording[];
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/recordings`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get recordings');
    }

    return response.json();
  }

  // Analytics and Monitoring
  async getRoomAnalytics(sessionId: string, token: string): Promise<{
    success: boolean;
    data: {
      analytics: SessionAnalytics;
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/analytics`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get analytics');
    }

    return response.json();
  }

  async reportConnectionIssue(
    sessionId: string, 
    issue: Omit<ConnectionIssue, 'timestamp'>, 
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/issue`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify(issue),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to report issue');
    }

    return response.json();
  }

  // Feedback
  async submitFeedback(sessionId: string, feedback: SessionFeedback, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/video/${sessionId}/feedback`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify(feedback),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to submit feedback');
    }

    return response.json();
  }

  // Statistics
  async getSessionStats(token: string): Promise<{
    success: boolean;
    data: {
      totalSessions: number;
      completedSessions: number;
      averageDuration: number;
      averageRating: number;
      connectionIssues: number;
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/video/stats`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get session stats');
    }

    return response.json();
  }

  async getUpcomingSessions(token: string): Promise<{
    success: boolean;
    data: {
      sessions: any[];
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/video/upcoming`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get upcoming sessions');
    }

    return response.json();
  }
}

export const videoAPI = new VideoAPI();

// Daily.co utilities
export class DailyUtils {
  static async loadDailyScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof window !== 'undefined' && (window as any).DailyIframe) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://unpkg.com/@daily-co/daily-js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Daily.co script'));
      document.head.appendChild(script);
    });
  }

  static async createCallObject(): Promise<any> {
    await this.loadDailyScript();
    const Daily = (window as any).DailyIframe;
    return Daily.createCallObject();
  }

  static formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  static getConnectionQuality(stats: any): 'excellent' | 'good' | 'fair' | 'poor' {
    if (!stats) return 'poor';
    
    const { packetLoss, roundTripTime } = stats;
    
    if (packetLoss < 0.01 && roundTripTime < 100) return 'excellent';
    if (packetLoss < 0.03 && roundTripTime < 200) return 'good';
    if (packetLoss < 0.05 && roundTripTime < 400) return 'fair';
    return 'poor';
  }

  static async checkMediaPermissions(): Promise<{
    camera: boolean;
    microphone: boolean;
    error?: string;
  }> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true,
      });
      
      // Stop all tracks immediately
      stream.getTracks().forEach(track => track.stop());
      
      return { camera: true, microphone: true };
    } catch (error: any) {
      if (error.name === 'NotAllowedError') {
        return { 
          camera: false, 
          microphone: false, 
          error: 'Camera and microphone permissions denied' 
        };
      }
      if (error.name === 'NotFoundError') {
        return { 
          camera: false, 
          microphone: false, 
          error: 'No camera or microphone found' 
        };
      }
      return { 
        camera: false, 
        microphone: false, 
        error: error.message || 'Failed to access media devices' 
      };
    }
  }

  static async getMediaDevices(): Promise<{
    cameras: MediaDeviceInfo[];
    microphones: MediaDeviceInfo[];
    speakers: MediaDeviceInfo[];
  }> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      
      return {
        cameras: devices.filter(device => device.kind === 'videoinput'),
        microphones: devices.filter(device => device.kind === 'audioinput'),
        speakers: devices.filter(device => device.kind === 'audiooutput'),
      };
    } catch (error) {
      console.error('Failed to enumerate devices:', error);
      return { cameras: [], microphones: [], speakers: [] };
    }
  }
}
