import { Request, Response, NextFunction } from 'express';
import { VideoService } from '@/services/videoService';
import { SessionService } from '@/services/sessionService';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

export class VideoController {
  /**
   * Create video room for session
   */
  static async createRoom(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      
      // Verify user has access to this session
      const { BookingService } = await import('@/services/bookingService');
      const session = await BookingService.getBooking(sessionId);
      
      const isClient = session.clientId.toString() === req.user._id.toString();
      const isCounselor = session.counselorId.toString() === req.user._id.toString();

      if (!isClient && !isCounselor) {
        return next(createError('Unauthorized to access this session', 403));
      }

      const room = await VideoService.createSessionRoom(sessionId);

      res.json({
        success: true,
        message: 'Video room created successfully',
        data: { room }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Generate meeting token for participant
   */
  static async generateToken(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      
      // Verify user has access to this session
      const { BookingService } = await import('@/services/bookingService');
      const session = await BookingService.getBooking(sessionId);
      
      const isClient = session.clientId.toString() === req.user._id.toString();
      const isCounselor = session.counselorId.toString() === req.user._id.toString();

      if (!isClient && !isCounselor) {
        return next(createError('Unauthorized to access this session', 403));
      }

      const role = isClient ? 'client' : 'counselor';
      const token = await VideoService.generateMeetingToken(
        sessionId,
        req.user._id.toString(),
        role
      );

      res.json({
        success: true,
        message: 'Meeting token generated successfully',
        data: { token }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Start recording
   */
  static async startRecording(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      
      // Verify user is counselor for this session
      const { BookingService } = await import('@/services/bookingService');
      const session = await BookingService.getBooking(sessionId);
      
      if (session.counselorId.toString() !== req.user._id.toString()) {
        return next(createError('Only counselors can start recording', 403));
      }

      await VideoService.startRecording(sessionId, req.user._id.toString());

      res.json({
        success: true,
        message: 'Recording started successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Stop recording
   */
  static async stopRecording(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      
      // Verify user is counselor for this session
      const { BookingService } = await import('@/services/bookingService');
      const session = await BookingService.getBooking(sessionId);
      
      if (session.counselorId.toString() !== req.user._id.toString()) {
        return next(createError('Only counselors can stop recording', 403));
      }

      await VideoService.stopRecording(sessionId, req.user._id.toString());

      res.json({
        success: true,
        message: 'Recording stopped successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get session recordings
   */
  static async getRecordings(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      
      // Verify user has access to this session
      const { BookingService } = await import('@/services/bookingService');
      const session = await BookingService.getBooking(sessionId);
      
      const isClient = session.clientId.toString() === req.user._id.toString();
      const isCounselor = session.counselorId.toString() === req.user._id.toString();
      const isAdmin = req.user.role === 'admin';

      if (!isClient && !isCounselor && !isAdmin) {
        return next(createError('Unauthorized to access recordings', 403));
      }

      const recordings = await VideoService.getSessionRecordings(sessionId);

      res.json({
        success: true,
        data: { recordings }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get room analytics
   */
  static async getRoomAnalytics(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      
      // Verify user is counselor or admin
      const { BookingService } = await import('@/services/bookingService');
      const session = await BookingService.getBooking(sessionId);
      
      const isCounselor = session.counselorId.toString() === req.user._id.toString();
      const isAdmin = req.user.role === 'admin';

      if (!isCounselor && !isAdmin) {
        return next(createError('Unauthorized to access analytics', 403));
      }

      const analytics = await VideoService.getRoomAnalytics(sessionId);

      res.json({
        success: true,
        data: { analytics }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Start session
   */
  static async startSession(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      
      // Verify user has access to this session
      const { BookingService } = await import('@/services/bookingService');
      const session = await BookingService.getBooking(sessionId);
      
      const isClient = session.clientId.toString() === req.user._id.toString();
      const isCounselor = session.counselorId.toString() === req.user._id.toString();

      if (!isClient && !isCounselor) {
        return next(createError('Unauthorized to start this session', 403));
      }

      const startedSession = await SessionService.startSession(
        sessionId,
        req.user._id.toString()
      );

      res.json({
        success: true,
        message: 'Session started successfully',
        data: { session: startedSession }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * End session
   */
  static async endSession(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      const sessionData = req.body;
      
      // Verify user has access to this session
      const { BookingService } = await import('@/services/bookingService');
      const session = await BookingService.getBooking(sessionId);
      
      const isClient = session.clientId.toString() === req.user._id.toString();
      const isCounselor = session.counselorId.toString() === req.user._id.toString();

      if (!isClient && !isCounselor) {
        return next(createError('Unauthorized to end this session', 403));
      }

      const endedSession = await SessionService.endSession(
        sessionId,
        req.user._id.toString(),
        sessionData
      );

      res.json({
        success: true,
        message: 'Session ended successfully',
        data: { session: endedSession }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Mark session as no-show
   */
  static async markNoShow(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      const { reason } = req.body;

      if (!reason) {
        return next(createError('Reason is required', 400));
      }
      
      // Verify user is counselor for this session
      const { BookingService } = await import('@/services/bookingService');
      const session = await BookingService.getBooking(sessionId);
      
      if (session.counselorId.toString() !== req.user._id.toString()) {
        return next(createError('Only counselors can mark sessions as no-show', 403));
      }

      const updatedSession = await SessionService.markNoShow(
        sessionId,
        req.user._id.toString(),
        reason
      );

      res.json({
        success: true,
        message: 'Session marked as no-show',
        data: { session: updatedSession }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Submit session feedback
   */
  static async submitFeedback(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      const feedback = req.body;
      
      const updatedSession = await SessionService.submitFeedback(
        sessionId,
        req.user._id.toString(),
        feedback
      );

      res.json({
        success: true,
        message: 'Feedback submitted successfully',
        data: { session: updatedSession }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get session statistics
   */
  static async getSessionStats(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const filters: any = {};

      // Filter by user role
      if (req.user.role === 'client') {
        filters.clientId = req.user._id.toString();
      } else if (req.user.role === 'counselor') {
        // Get counselor ID from user
        const { CounselorService } = await import('@/services/counselorService');
        const counselor = await CounselorService.getCounselorByUserId(req.user._id.toString());
        filters.counselorId = counselor._id.toString();
      }

      // Add query filters
      if (req.query.dateFrom) filters.dateFrom = req.query.dateFrom;
      if (req.query.dateTo) filters.dateTo = req.query.dateTo;

      const stats = await SessionService.getSessionStats(filters);

      res.json({
        success: true,
        data: { stats }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get upcoming sessions
   */
  static async getUpcomingSessions(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const role = req.user.role === 'counselor' ? 'counselor' : 'client';
      const sessions = await SessionService.getUpcomingSessions(
        req.user._id.toString(),
        role
      );

      res.json({
        success: true,
        data: { sessions }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Handle Daily.co webhook
   */
  static async handleDailyWebhook(req: Request, res: Response, next: NextFunction) {
    try {
      const signature = req.headers['x-daily-signature'] as string;
      const payload = JSON.stringify(req.body);

      // Verify webhook signature
      if (!VideoService.verifyWebhook(payload, signature)) {
        return next(createError('Invalid webhook signature', 400));
      }

      const event = req.body;

      switch (event.type) {
        case 'room.exp':
          // Room expired
          logger.info(`Room expired: ${event.room}`);
          break;

        case 'recording.started':
          // Recording started
          logger.info(`Recording started: ${event.room}`);
          break;

        case 'recording.stopped':
          // Recording stopped
          logger.info(`Recording stopped: ${event.room}`);
          break;

        case 'recording.ready-to-download':
          // Recording ready for download
          logger.info(`Recording ready: ${event.room}`);
          break;

        default:
          logger.info(`Unhandled Daily.co webhook event: ${event.type}`);
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Daily.co webhook error:', error);
      res.status(200).json({ success: false }); // Always return 200 to Daily.co
    }
  }
}
