'use client';

import { useState, useEffect, useRef } from 'react';
import { useAuthStore } from '@/store/authStore';
import { useSocket } from '@/hooks/useSocket';
import { chatAPI } from '@/lib/chat';
import { ChatRoom as ChatRoomType, ChatMessage, TypingUser } from '@/types/chat';
import MessageItem from './MessageItem';
import MessageInput from './MessageInput';

interface ChatRoomProps {
  roomId: string;
}

export default function ChatRoom({ roomId }: ChatRoomProps) {
  const { tokens, guestToken, user, isGuest } = useAuthStore();
  const [room, setRoom] = useState<ChatRoomType | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [replyTo, setReplyTo] = useState<ChatMessage | null>(null);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [showParticipants, setShowParticipants] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const token = tokens?.accessToken || guestToken;

  const {
    isConnected,
    sendMessage,
    startTyping,
    stopTyping,
  } = useSocket({
    roomId,
    onNewMessage: (message: ChatMessage) => {
      setMessages(prev => [...prev, message]);
      scrollToBottom();
    },
    onTypingStart: (user: TypingUser) => {
      setTypingUsers(prev => {
        const exists = prev.some(u => 
          (u.userId && u.userId === user.userId) || 
          (u.anonymousId && u.anonymousId === user.anonymousId)
        );
        return exists ? prev : [...prev, user];
      });
    },
    onTypingStop: (user: TypingUser) => {
      setTypingUsers(prev => prev.filter(u => 
        !((u.userId && u.userId === user.userId) || 
          (u.anonymousId && u.anonymousId === user.anonymousId))
      ));
    },
    onError: (error: string) => {
      setError(error);
    },
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    fetchRoomData();
    fetchMessages();
  }, [roomId, token]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchRoomData = async () => {
    try {
      const response = await chatAPI.getChatRoom(roomId, token);
      setRoom(response.data.chatRoom);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load room');
    }
  };

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const response = await chatAPI.getChatMessages(roomId, 1, 50, token);
      setMessages(response.data.messages.reverse()); // Reverse to show oldest first
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = (content: { text: string; type: 'text' }) => {
    if (!isConnected) {
      setError('Not connected to chat server');
      return;
    }

    sendMessage(roomId, content, replyTo?._id);
    setReplyTo(null);
  };

  const handleReply = (message: ChatMessage) => {
    setReplyTo(message);
  };

  const handleReport = async (messageId: string) => {
    try {
      await chatAPI.reportMessage(messageId, 'inappropriate', undefined, token);
      // Show success message
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to report message');
    }
  };

  const handleReaction = (messageId: string, emoji: string) => {
    // This would typically be handled through the socket
    console.log('Add reaction:', messageId, emoji);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <svg className="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!room) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Room not found</h3>
          <p className="text-gray-600">This chat room may have been removed or you don't have access.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">{room.name}</h2>
            <p className="text-sm text-gray-600">
              {room.currentParticipants} participants • {isConnected ? 'Connected' : 'Connecting...'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowParticipants(!showParticipants)}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
            title="Show participants"
          >
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
            <p className="text-gray-600">Be the first to start the conversation!</p>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <MessageItem
                key={message._id}
                message={message}
                onReply={handleReply}
                onReport={handleReport}
                onReaction={handleReaction}
              />
            ))}
            
            {/* Typing Indicators */}
            {typingUsers.length > 0 && (
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span>
                  {typingUsers.length === 1 
                    ? `${typingUsers[0].displayName} is typing...`
                    : `${typingUsers.length} people are typing...`
                  }
                </span>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        onTypingStart={() => startTyping(roomId)}
        onTypingStop={() => stopTyping(roomId)}
        replyTo={replyTo}
        onCancelReply={() => setReplyTo(null)}
        disabled={!isConnected}
        placeholder={isConnected ? "Type your message..." : "Connecting..."}
      />
    </div>
  );
}
