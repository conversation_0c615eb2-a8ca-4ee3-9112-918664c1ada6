import { User, IUser } from '@/models/User';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import { uploadToCloudinary, deleteFromCloudinary } from '@/utils/cloudinary';

export interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
  areasOfInterest?: string[];
  location?: {
    country?: string;
    city?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  preferences?: {
    notifications?: {
      email?: boolean;
      push?: boolean;
      sessionReminders?: boolean;
      chatMessages?: boolean;
    };
    privacy?: {
      showOnlineStatus?: boolean;
      allowDirectMessages?: boolean;
    };
  };
}

export interface UserFilters {
  role?: string;
  isActive?: boolean;
  isEmailVerified?: boolean;
  areasOfInterest?: string[];
  country?: string;
  search?: string;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class UserService {
  /**
   * Get user profile by ID
   */
  static async getUserProfile(userId: string): Promise<IUser> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      return user;
    } catch (error) {
      logger.error('Get user profile error:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(userId: string, updateData: UpdateProfileData): Promise<IUser> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      // Update basic fields
      if (updateData.firstName) user.firstName = updateData.firstName;
      if (updateData.lastName) user.lastName = updateData.lastName;
      if (updateData.areasOfInterest) user.areasOfInterest = updateData.areasOfInterest;

      // Update location
      if (updateData.location) {
        user.location = {
          ...user.location,
          ...updateData.location
        };
      }

      // Update preferences
      if (updateData.preferences) {
        if (updateData.preferences.notifications) {
          user.preferences.notifications = {
            ...user.preferences.notifications,
            ...updateData.preferences.notifications
          };
        }
        
        if (updateData.preferences.privacy) {
          user.preferences.privacy = {
            ...user.preferences.privacy,
            ...updateData.preferences.privacy
          };
        }
      }

      await user.save();
      logger.info(`User profile updated: ${user.email}`);

      return user;
    } catch (error) {
      logger.error('Update profile error:', error);
      throw error;
    }
  }

  /**
   * Upload profile picture
   */
  static async uploadProfilePicture(userId: string, file: Express.Multer.File): Promise<string> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      // Delete old profile picture if exists
      if (user.profilePicture) {
        try {
          await deleteFromCloudinary(user.profilePicture);
        } catch (deleteError) {
          logger.warn('Failed to delete old profile picture:', deleteError);
        }
      }

      // Upload new profile picture
      const imageUrl = await uploadToCloudinary(file, 'profile-pictures');
      
      user.profilePicture = imageUrl;
      await user.save();

      logger.info(`Profile picture updated for user: ${user.email}`);
      return imageUrl;
    } catch (error) {
      logger.error('Upload profile picture error:', error);
      throw error;
    }
  }

  /**
   * Delete profile picture
   */
  static async deleteProfilePicture(userId: string): Promise<void> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      if (user.profilePicture) {
        try {
          await deleteFromCloudinary(user.profilePicture);
        } catch (deleteError) {
          logger.warn('Failed to delete profile picture from cloud:', deleteError);
        }

        user.profilePicture = undefined;
        await user.save();

        logger.info(`Profile picture deleted for user: ${user.email}`);
      }
    } catch (error) {
      logger.error('Delete profile picture error:', error);
      throw error;
    }
  }

  /**
   * Get users with filters and pagination
   */
  static async getUsers(filters: UserFilters = {}, options: PaginationOptions = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      // Build query
      const query: any = {};

      if (filters.role) query.role = filters.role;
      if (filters.isActive !== undefined) query.isActive = filters.isActive;
      if (filters.isEmailVerified !== undefined) query.isEmailVerified = filters.isEmailVerified;
      if (filters.areasOfInterest?.length) query.areasOfInterest = { $in: filters.areasOfInterest };
      if (filters.country) query['location.country'] = filters.country;

      // Search functionality
      if (filters.search) {
        query.$or = [
          { firstName: { $regex: filters.search, $options: 'i' } },
          { lastName: { $regex: filters.search, $options: 'i' } },
          { username: { $regex: filters.search, $options: 'i' } },
          { email: { $regex: filters.search, $options: 'i' } }
        ];
      }

      // Execute query with pagination
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

      const [users, total] = await Promise.all([
        User.find(query)
          .select('-password -emailVerificationToken -passwordResetToken')
          .sort(sortOptions)
          .skip(skip)
          .limit(limit),
        User.countDocuments(query)
      ]);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      };
    } catch (error) {
      logger.error('Get users error:', error);
      throw error;
    }
  }

  /**
   * Deactivate user account
   */
  static async deactivateAccount(userId: string): Promise<void> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      user.isActive = false;
      await user.save();

      logger.info(`User account deactivated: ${user.email}`);
    } catch (error) {
      logger.error('Deactivate account error:', error);
      throw error;
    }
  }

  /**
   * Reactivate user account
   */
  static async reactivateAccount(userId: string): Promise<void> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      user.isActive = true;
      await user.save();

      logger.info(`User account reactivated: ${user.email}`);
    } catch (error) {
      logger.error('Reactivate account error:', error);
      throw error;
    }
  }

  /**
   * Delete user account permanently
   */
  static async deleteAccount(userId: string): Promise<void> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      // Delete profile picture if exists
      if (user.profilePicture) {
        try {
          await deleteFromCloudinary(user.profilePicture);
        } catch (deleteError) {
          logger.warn('Failed to delete profile picture during account deletion:', deleteError);
        }
      }

      // TODO: Clean up related data (sessions, messages, etc.)
      // This should be implemented when those services are created

      await User.findByIdAndDelete(userId);
      logger.info(`User account deleted permanently: ${user.email}`);
    } catch (error) {
      logger.error('Delete account error:', error);
      throw error;
    }
  }

  /**
   * Get user statistics
   */
  static async getUserStats() {
    try {
      const stats = await User.aggregate([
        {
          $group: {
            _id: null,
            totalUsers: { $sum: 1 },
            activeUsers: {
              $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
            },
            verifiedUsers: {
              $sum: { $cond: [{ $eq: ['$isEmailVerified', true] }, 1, 0] }
            },
            counselors: {
              $sum: { $cond: [{ $eq: ['$role', 'counselor'] }, 1, 0] }
            },
            admins: {
              $sum: { $cond: [{ $in: ['$role', ['admin', 'superadmin']] }, 1, 0] }
            }
          }
        }
      ]);

      const roleStats = await User.aggregate([
        {
          $group: {
            _id: '$role',
            count: { $sum: 1 }
          }
        }
      ]);

      const monthlySignups = await User.aggregate([
        {
          $match: {
            createdAt: {
              $gte: new Date(new Date().getFullYear(), new Date().getMonth() - 11, 1)
            }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            count: { $sum: 1 }
          }
        },
        {
          $sort: { '_id.year': 1, '_id.month': 1 }
        }
      ]);

      return {
        overview: stats[0] || {
          totalUsers: 0,
          activeUsers: 0,
          verifiedUsers: 0,
          counselors: 0,
          admins: 0
        },
        roleDistribution: roleStats,
        monthlySignups
      };
    } catch (error) {
      logger.error('Get user stats error:', error);
      throw error;
    }
  }

  /**
   * Update user role (admin only)
   */
  static async updateUserRole(userId: string, newRole: string): Promise<IUser> {
    try {
      const validRoles = ['user', 'counselor', 'admin', 'superadmin'];
      
      if (!validRoles.includes(newRole)) {
        throw createError('Invalid role', 400);
      }

      const user = await User.findById(userId);
      
      if (!user) {
        throw createError('User not found', 404);
      }

      const oldRole = user.role;
      user.role = newRole as any;
      await user.save();

      logger.info(`User role updated from ${oldRole} to ${newRole} for user: ${user.email}`);
      return user;
    } catch (error) {
      logger.error('Update user role error:', error);
      throw error;
    }
  }
}
