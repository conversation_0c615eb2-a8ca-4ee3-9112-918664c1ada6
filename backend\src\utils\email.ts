import nodemailer from 'nodemailer';
import { logger } from '@/utils/logger';

export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  from?: string;
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.EMAIL_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Verify connection configuration
    this.verifyConnection();
  }

  private async verifyConnection(): Promise<void> {
    try {
      await this.transporter.verify();
      logger.info('Email service connected successfully');
    } catch (error) {
      logger.error('Email service connection failed:', error);
    }
  }

  async sendEmail(options: EmailOptions): Promise<void> {
    try {
      const mailOptions = {
        from: options.from || process.env.EMAIL_FROM || process.env.EMAIL_USER,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html || options.text
      };

      const info = await this.transporter.sendMail(mailOptions);
      logger.info(`Email sent successfully to ${options.to}:`, info.messageId);
    } catch (error) {
      logger.error(`Failed to send email to ${options.to}:`, error);
      throw new Error('Failed to send email');
    }
  }

  async sendWelcomeEmail(email: string, firstName: string): Promise<void> {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #7916ff, #843dff); padding: 40px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to Theramea!</h1>
        </div>
        
        <div style="padding: 40px; background-color: #f9f9f9;">
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${firstName},</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            Welcome to Theramea - your safe space for mental health support and professional counseling.
          </p>
          
          <div style="background: white; padding: 30px; border-radius: 8px; margin: 30px 0;">
            <h3 style="color: #7916ff; margin-bottom: 15px;">What you can do now:</h3>
            <ul style="color: #666; line-height: 1.8;">
              <li>Join anonymous group chats for peer support</li>
              <li>Explore our self-help library with articles and tools</li>
              <li>Book 1-on-1 sessions with certified counselors</li>
              <li>Complete your profile to get personalized recommendations</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/dashboard" 
               style="background-color: #7916ff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
              Get Started
            </a>
          </div>
          
          <p style="color: #666; line-height: 1.6; margin-top: 30px;">
            Remember, seeking help is a sign of strength. We're here to support you on your journey.
          </p>
          
          <p style="color: #666; line-height: 1.6;">
            Best regards,<br>
            The Theramea Team
          </p>
        </div>
        
        <div style="background-color: #333; color: #999; padding: 20px; text-align: center; font-size: 12px;">
          <p>© 2024 Theramea. All rights reserved.</p>
          <p>Making professional counseling accessible to everyone.</p>
        </div>
      </div>
    `;

    await this.sendEmail({
      to: email,
      subject: 'Welcome to Theramea - Your Mental Health Journey Starts Here',
      html
    });
  }

  async sendSessionReminderEmail(email: string, firstName: string, sessionDetails: any): Promise<void> {
    const sessionDate = new Date(sessionDetails.scheduledAt).toLocaleDateString();
    const sessionTime = new Date(sessionDetails.scheduledAt).toLocaleTimeString();

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #7916ff; padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">Session Reminder</h1>
        </div>
        
        <div style="padding: 30px; background-color: #f9f9f9;">
          <h2 style="color: #333;">Hi ${firstName},</h2>
          
          <p style="color: #666; line-height: 1.6;">
            This is a reminder that you have a counseling session scheduled:
          </p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #7916ff;">
            <h3 style="color: #7916ff; margin: 0 0 10px 0;">Session Details</h3>
            <p style="margin: 5px 0; color: #666;"><strong>Date:</strong> ${sessionDate}</p>
            <p style="margin: 5px 0; color: #666;"><strong>Time:</strong> ${sessionTime}</p>
            <p style="margin: 5px 0; color: #666;"><strong>Duration:</strong> ${sessionDetails.duration} minutes</p>
            <p style="margin: 5px 0; color: #666;"><strong>Counselor:</strong> ${sessionDetails.counselorName}</p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/sessions/${sessionDetails.id}" 
               style="background-color: #7916ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Join Session
            </a>
          </div>
          
          <p style="color: #666; line-height: 1.6; font-size: 14px;">
            Please join the session 5 minutes early to ensure a smooth start. If you need to reschedule, please do so at least 12 hours in advance.
          </p>
        </div>
      </div>
    `;

    await this.sendEmail({
      to: email,
      subject: `Session Reminder - ${sessionDate} at ${sessionTime}`,
      html
    });
  }

  async sendPasswordResetSuccessEmail(email: string, firstName: string): Promise<void> {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #22c55e; padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">Password Reset Successful</h1>
        </div>
        
        <div style="padding: 30px; background-color: #f9f9f9;">
          <h2 style="color: #333;">Hi ${firstName},</h2>
          
          <p style="color: #666; line-height: 1.6;">
            Your password has been successfully reset. You can now log in to your Theramea account with your new password.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/login" 
               style="background-color: #7916ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Log In
            </a>
          </div>
          
          <p style="color: #666; line-height: 1.6; font-size: 14px;">
            If you didn't reset your password, please contact our support team immediately.
          </p>
        </div>
      </div>
    `;

    await this.sendEmail({
      to: email,
      subject: 'Password Reset Successful - Theramea',
      html
    });
  }
}

// Create singleton instance
const emailService = new EmailService();

// Export convenience function
export const sendEmail = (options: EmailOptions): Promise<void> => {
  return emailService.sendEmail(options);
};

export const sendWelcomeEmail = (email: string, firstName: string): Promise<void> => {
  return emailService.sendWelcomeEmail(email, firstName);
};

export const sendSessionReminderEmail = (email: string, firstName: string, sessionDetails: any): Promise<void> => {
  return emailService.sendSessionReminderEmail(email, firstName, sessionDetails);
};

export const sendPasswordResetSuccessEmail = (email: string, firstName: string): Promise<void> => {
  return emailService.sendPasswordResetSuccessEmail(email, firstName);
};

export default emailService;
