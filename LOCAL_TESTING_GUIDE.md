# 🧪 Theramea Local Testing Guide

## 📋 Pre-Testing Setup

### 1. Environment Variables Setup

Create `.env.local` files in both frontend and backend directories:

#### Backend `.env`
```bash
# Database
MONGODB_URI=mongodb://localhost:27017/theramea_dev
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-super-secret-jwt-key-for-development
JWT_REFRESH_SECRET=your-super-secret-refresh-key-for-development

# Email (for testing - use Mailtrap or similar)
EMAIL_HOST=smtp.mailtrap.io
EMAIL_PORT=2525
EMAIL_USER=your-mailtrap-username
EMAIL_PASS=your-mailtrap-password
EMAIL_FROM=<EMAIL>

# Paystack (use test keys)
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key

# Daily.co (use test domain)
DAILY_API_KEY=your_daily_api_key
DAILY_DOMAIN=your-test-domain.daily.co

# File Upload (local storage for testing)
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# Server
PORT=5000
NODE_ENV=development
```

#### Frontend `.env.local`
```bash
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_SOCKET_URL=http://localhost:5000
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key
NEXT_PUBLIC_DAILY_DOMAIN=your-test-domain.daily.co
```

### 2. Database Setup

#### MongoDB Setup
```bash
# Install MongoDB locally or use Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Or install MongoDB Community Edition
# https://docs.mongodb.com/manual/installation/
```

#### Redis Setup
```bash
# Install Redis locally or use Docker
docker run -d -p 6379:6379 --name redis redis:alpine

# Or install Redis locally
# https://redis.io/download
```

### 3. Install Dependencies

```bash
# Backend
cd backend
npm install

# Frontend
cd ../frontend
npm install
```

## 🚀 Starting the Development Servers

### 1. Start Backend Server
```bash
cd backend
npm run dev
```
Server should start on `http://localhost:5000`

### 2. Start Frontend Server
```bash
cd frontend
npm run dev
```
Frontend should start on `http://localhost:3000`

## ✅ Testing Checklist

### 1. **Authentication System Testing**

#### User Registration
- [ ] Register new user with valid email
- [ ] Test email validation
- [ ] Test password strength requirements
- [ ] Test duplicate email handling
- [ ] Verify email confirmation flow

#### User Login
- [ ] Login with valid credentials
- [ ] Test invalid email/password
- [ ] Test account lockout after failed attempts
- [ ] Test "Remember Me" functionality
- [ ] Test logout functionality

#### Guest Access
- [ ] Access platform as guest
- [ ] Test guest limitations
- [ ] Test guest to registered user conversion

### 2. **User Dashboard Testing**

#### Dashboard Features
- [ ] View upcoming sessions
- [ ] View recent activity
- [ ] Access quick actions
- [ ] View profile information
- [ ] Navigate to different sections

#### Profile Management
- [ ] Update profile information
- [ ] Upload profile picture
- [ ] Change password
- [ ] Update preferences
- [ ] Delete account

### 3. **Counselor Discovery & Booking**

#### Counselor Browsing
- [ ] View counselor list
- [ ] Filter by specialization
- [ ] Filter by session type
- [ ] Filter by language
- [ ] Filter by rating
- [ ] Filter by price range
- [ ] Search by name

#### Counselor Profiles
- [ ] View detailed counselor profile
- [ ] View qualifications and licenses
- [ ] View specializations
- [ ] View availability
- [ ] View ratings and reviews

#### Booking Process
- [ ] Select counselor
- [ ] Choose session type
- [ ] Select date and time
- [ ] Review booking details
- [ ] Proceed to payment

### 4. **Payment System Testing**

#### Payment Flow
- [ ] Initialize payment
- [ ] Test different payment methods
- [ ] Complete payment successfully
- [ ] Handle payment failures
- [ ] Test payment verification

#### Payment Management
- [ ] View payment history
- [ ] Request refunds
- [ ] View transaction details
- [ ] Download receipts

### 5. **Video Session Testing**

#### Session Preparation
- [ ] Access session 15 minutes before
- [ ] Test camera and microphone
- [ ] Join session successfully
- [ ] Handle permission errors

#### Video Call Features
- [ ] Audio mute/unmute
- [ ] Video on/off
- [ ] Screen sharing
- [ ] Recording (counselor only)
- [ ] Chat messaging
- [ ] End session

### 6. **Chatroom Testing**

#### Room Access
- [ ] Join public chatrooms
- [ ] View room participants
- [ ] Send messages
- [ ] Receive real-time messages

#### Chat Features
- [ ] Text formatting
- [ ] Emoji reactions
- [ ] Reply to messages
- [ ] Mention users
- [ ] Typing indicators
- [ ] Message moderation

### 7. **Self-Help Library Testing**

#### Resource Browsing
- [ ] View resource categories
- [ ] Filter resources
- [ ] Search resources
- [ ] View resource details

#### Resource Interaction
- [ ] Read articles
- [ ] Watch videos
- [ ] Download resources
- [ ] Rate and review
- [ ] Save favorites

### 8. **Counselor Portal Testing**

#### Registration Process
- [ ] Complete counselor registration
- [ ] Upload verification documents
- [ ] Submit for approval
- [ ] Handle rejection/approval

#### Dashboard Features
- [ ] View statistics
- [ ] Manage upcoming sessions
- [ ] Track earnings
- [ ] View client feedback

#### Profile Management
- [ ] Update professional profile
- [ ] Manage availability
- [ ] Set pricing
- [ ] Update qualifications

### 9. **Admin Panel Testing**

#### User Management
- [ ] View user list
- [ ] Filter and search users
- [ ] Update user roles
- [ ] Activate/deactivate accounts

#### Counselor Management
- [ ] Review pending applications
- [ ] Approve/reject counselors
- [ ] Suspend counselors
- [ ] View counselor statistics

#### System Monitoring
- [ ] View platform statistics
- [ ] Monitor sessions
- [ ] Handle reports
- [ ] Manage content

## 🐛 Common Issues & Solutions

### Backend Issues

#### Database Connection
```bash
# Check MongoDB status
mongosh
# or
docker ps | grep mongodb
```

#### Redis Connection
```bash
# Check Redis status
redis-cli ping
# or
docker ps | grep redis
```

#### Port Conflicts
```bash
# Check what's running on port 5000
lsof -i :5000
# Kill process if needed
kill -9 <PID>
```

### Frontend Issues

#### API Connection
- Check `NEXT_PUBLIC_API_URL` in `.env.local`
- Verify backend server is running
- Check browser console for CORS errors

#### Socket Connection
- Verify `NEXT_PUBLIC_SOCKET_URL` is correct
- Check WebSocket connection in browser dev tools
- Ensure backend socket server is running

### Payment Testing

#### Paystack Test Cards
```
# Successful payment
****************

# Insufficient funds
**************** (with amount > 300000)

# Invalid card
****************
```

### Video Session Testing

#### Daily.co Setup
1. Create account at daily.co
2. Get API key from dashboard
3. Create test domain
4. Update environment variables

## 📊 Testing Tools

### Manual Testing
- Browser developer tools
- Network tab for API calls
- Console for errors
- Application tab for storage

### Automated Testing (Future)
```bash
# Unit tests
npm run test

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e
```

## 🔍 Debugging Tips

### Backend Debugging
```bash
# Enable debug logs
DEBUG=* npm run dev

# Check specific modules
DEBUG=auth,payment npm run dev
```

### Frontend Debugging
- Use React Developer Tools
- Check Redux DevTools (if using Redux)
- Monitor network requests
- Check console for errors

### Database Debugging
```bash
# MongoDB queries
mongosh theramea_dev
db.users.find()
db.sessions.find()

# Redis debugging
redis-cli
keys *
```

## 📝 Test Data Creation

### Create Test Users
```javascript
// In MongoDB shell
db.users.insertOne({
  firstName: "Test",
  lastName: "User",
  email: "<EMAIL>",
  password: "$2b$10$hashedpassword", // Use bcrypt
  role: "user",
  isActive: true,
  isEmailVerified: true
})
```

### Create Test Counselor
```javascript
// Create counselor profile after user creation
db.counselors.insertOne({
  userId: ObjectId("user_id_here"),
  bio: "Test counselor bio",
  specializations: ["anxiety", "depression"],
  verification: { status: "approved" },
  pricing: { currency: "NGN", ratePerMinute: 100 }
})
```

## 🎯 Testing Priority

1. **High Priority**: Authentication, Payment, Video Sessions
2. **Medium Priority**: Booking, Chatrooms, Admin Panel
3. **Low Priority**: Self-help Library, Advanced Features

Start with high-priority features and work your way down!
