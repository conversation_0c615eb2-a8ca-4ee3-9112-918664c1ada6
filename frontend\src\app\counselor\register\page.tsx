"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { counselorPortalAPI } from "@/lib/counselorPortal";
import {
  COUNSELOR_SPECIALIZATIONS,
  SESSION_TYPES,
  LANGUAGES,
} from "@/types/counselor";
import Header from "@/components/layout/Header";

export default function CounselorRegistrationPage() {
  const router = useRouter();
  const { isAuthenticated, user, checkAuth, isLoading, tokens } =
    useAuthStore();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    // Basic Information
    bio: "",
    specializations: [] as string[],

    // Experience & Qualifications
    experience: {
      years: 0,
      description: "",
    },
    qualifications: [
      {
        degree: "",
        institution: "",
        year: new Date().getFullYear(),
      },
    ],
    licenses: [
      {
        type: "",
        number: "",
        issuingAuthority: "",
        expiryDate: "",
      },
    ],

    // Profile & Approach
    profile: {
      languages: ["english"] as string[],
      approachDescription: "",
      sessionTypes: ["individual"] as string[],
    },

    // Pricing
    pricing: {
      currency: "NGN" as "NGN" | "USD",
      ratePerMinute: 0,
      minimumSessionDuration: 30,
    },

    // Bank Details
    bankDetails: {
      accountName: "",
      accountNumber: "",
      bankName: "",
      bankCode: "",
      currency: "NGN" as "NGN" | "USD",
    },
  });

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      router.push("/auth/login");
    }
  }, [isAuthenticated, isLoading, router]);

  const handleInputChange = (field: string, value: any) => {
    const keys = field.split(".");
    if (keys.length === 1) {
      setFormData((prev) => ({ ...prev, [field]: value }));
    } else if (keys.length === 2) {
      setFormData((prev) => ({
        ...prev,
        [keys[0]]: {
          ...prev[keys[0] as keyof typeof prev],
          [keys[1]]: value,
        },
      }));
    }
  };

  const handleArrayChange = (field: string, value: string[]) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const addQualification = () => {
    setFormData((prev) => ({
      ...prev,
      qualifications: [
        ...prev.qualifications,
        {
          degree: "",
          institution: "",
          year: new Date().getFullYear(),
        },
      ],
    }));
  };

  const removeQualification = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      qualifications: prev.qualifications.filter((_, i) => i !== index),
    }));
  };

  const addLicense = () => {
    setFormData((prev) => ({
      ...prev,
      licenses: [
        ...prev.licenses,
        {
          type: "",
          number: "",
          issuingAuthority: "",
          expiryDate: "",
        },
      ],
    }));
  };

  const removeLicense = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      licenses: prev.licenses.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tokens?.accessToken) {
      setError("Authentication required");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await counselorPortalAPI.registerAsCounselor(
        formData,
        tokens.accessToken
      );

      // Redirect to pending approval page
      router.push("/counselor/pending-approval");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Registration failed");
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  const steps = [
    { id: 1, name: "Basic Information", description: "Tell us about yourself" },
    {
      id: 2,
      name: "Qualifications",
      description: "Your education and licenses",
    },
    {
      id: 3,
      name: "Profile & Pricing",
      description: "Your approach and rates",
    },
    { id: 4, name: "Bank Details", description: "Payment information" },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Become a Counselor
          </h1>
          <p className="text-gray-600">
            Join our platform and help people on their mental health journey
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    currentStep >= step.id
                      ? "bg-purple-600 border-purple-600 text-white"
                      : "border-gray-300 text-gray-500"
                  }`}
                >
                  {currentStep > step.id ? (
                    <svg
                      className="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    step.id
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <p
                    className={`text-sm font-medium ${
                      currentStep >= step.id
                        ? "text-purple-600"
                        : "text-gray-500"
                    }`}
                  >
                    {step.name}
                  </p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`hidden sm:block w-16 h-0.5 ml-4 ${
                      currentStep > step.id ? "bg-purple-600" : "bg-gray-300"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <form onSubmit={handleSubmit}>
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Basic Information
                </h2>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Professional Bio *
                  </label>
                  <textarea
                    value={formData.bio}
                    onChange={(e) => handleInputChange("bio", e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Tell potential clients about your background, approach, and what makes you unique..."
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Maximum 1000 characters
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Specializations * (Select at least 2)
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {COUNSELOR_SPECIALIZATIONS.map((spec) => (
                      <label
                        key={spec.value}
                        className="flex items-center space-x-2"
                      >
                        <input
                          type="checkbox"
                          checked={formData.specializations.includes(
                            spec.value
                          )}
                          onChange={(e) => {
                            if (e.target.checked) {
                              handleArrayChange("specializations", [
                                ...formData.specializations,
                                spec.value,
                              ]);
                            } else {
                              handleArrayChange(
                                "specializations",
                                formData.specializations.filter(
                                  (s) => s !== spec.value
                                )
                              );
                            }
                          }}
                          className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                        />
                        <span className="text-sm text-gray-700">
                          {spec.icon} {spec.label}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Years of Experience *
                    </label>
                    <input
                      type="number"
                      value={formData.experience.years}
                      onChange={(e) =>
                        handleInputChange(
                          "experience.years",
                          parseInt(e.target.value)
                        )
                      }
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Experience Description *
                  </label>
                  <textarea
                    value={formData.experience.description}
                    onChange={(e) =>
                      handleInputChange(
                        "experience.description",
                        e.target.value
                      )
                    }
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Describe your professional experience, previous roles, and areas of expertise..."
                    required
                  />
                </div>
              </div>
            )}

            {/* Step 2: Qualifications */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Qualifications & Licenses
                </h2>

                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Education
                    </h3>
                    <button
                      type="button"
                      onClick={addQualification}
                      className="text-purple-600 hover:text-purple-700 text-sm font-medium"
                    >
                      + Add Qualification
                    </button>
                  </div>

                  {formData.qualifications.map((qual, index) => (
                    <div
                      key={index}
                      className="border border-gray-200 rounded-lg p-4 mb-4"
                    >
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Degree *
                          </label>
                          <input
                            type="text"
                            value={qual.degree}
                            onChange={(e) => {
                              const newQuals = [...formData.qualifications];
                              newQuals[index].degree = e.target.value;
                              setFormData((prev) => ({
                                ...prev,
                                qualifications: newQuals,
                              }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                            placeholder="e.g., Master of Psychology"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Institution *
                          </label>
                          <input
                            type="text"
                            value={qual.institution}
                            onChange={(e) => {
                              const newQuals = [...formData.qualifications];
                              newQuals[index].institution = e.target.value;
                              setFormData((prev) => ({
                                ...prev,
                                qualifications: newQuals,
                              }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                            placeholder="University name"
                            required
                          />
                        </div>
                        <div className="flex items-end space-x-2">
                          <div className="flex-1">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Year *
                            </label>
                            <input
                              type="number"
                              value={qual.year}
                              onChange={(e) => {
                                const newQuals = [...formData.qualifications];
                                newQuals[index].year = parseInt(e.target.value);
                                setFormData((prev) => ({
                                  ...prev,
                                  qualifications: newQuals,
                                }));
                              }}
                              min="1950"
                              max={new Date().getFullYear()}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                              required
                            />
                          </div>
                          {formData.qualifications.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeQualification(index)}
                              className="text-red-600 hover:text-red-700 p-2"
                            >
                              <svg
                                className="w-5 h-5"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Professional Licenses
                    </h3>
                    <button
                      type="button"
                      onClick={addLicense}
                      className="text-purple-600 hover:text-purple-700 text-sm font-medium"
                    >
                      + Add License
                    </button>
                  </div>

                  {formData.licenses.map((license, index) => (
                    <div
                      key={index}
                      className="border border-gray-200 rounded-lg p-4 mb-4"
                    >
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            License Type *
                          </label>
                          <input
                            type="text"
                            value={license.type}
                            onChange={(e) => {
                              const newLicenses = [...formData.licenses];
                              newLicenses[index].type = e.target.value;
                              setFormData((prev) => ({
                                ...prev,
                                licenses: newLicenses,
                              }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                            placeholder="e.g., Licensed Professional Counselor"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            License Number *
                          </label>
                          <input
                            type="text"
                            value={license.number}
                            onChange={(e) => {
                              const newLicenses = [...formData.licenses];
                              newLicenses[index].number = e.target.value;
                              setFormData((prev) => ({
                                ...prev,
                                licenses: newLicenses,
                              }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Issuing Authority *
                          </label>
                          <input
                            type="text"
                            value={license.issuingAuthority}
                            onChange={(e) => {
                              const newLicenses = [...formData.licenses];
                              newLicenses[index].issuingAuthority =
                                e.target.value;
                              setFormData((prev) => ({
                                ...prev,
                                licenses: newLicenses,
                              }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                            placeholder="e.g., State Board of Psychology"
                            required
                          />
                        </div>
                        <div className="flex items-end space-x-2">
                          <div className="flex-1">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Expiry Date *
                            </label>
                            <input
                              type="date"
                              value={license.expiryDate}
                              onChange={(e) => {
                                const newLicenses = [...formData.licenses];
                                newLicenses[index].expiryDate = e.target.value;
                                setFormData((prev) => ({
                                  ...prev,
                                  licenses: newLicenses,
                                }));
                              }}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                              required
                            />
                          </div>
                          {formData.licenses.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeLicense(index)}
                              className="text-red-600 hover:text-red-700 p-2"
                            >
                              <svg
                                className="w-5 h-5"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Step 3: Profile & Pricing */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Profile & Pricing
                </h2>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Therapeutic Approach *
                  </label>
                  <textarea
                    value={formData.profile.approachDescription}
                    onChange={(e) =>
                      handleInputChange(
                        "profile.approachDescription",
                        e.target.value
                      )
                    }
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Describe your therapeutic approach, techniques you use, and your philosophy..."
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Maximum 500 characters
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Languages Spoken *
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {LANGUAGES.map((lang) => (
                      <label
                        key={lang.value}
                        className="flex items-center space-x-2"
                      >
                        <input
                          type="checkbox"
                          checked={formData.profile.languages.includes(
                            lang.value
                          )}
                          onChange={(e) => {
                            if (e.target.checked) {
                              handleInputChange("profile.languages", [
                                ...formData.profile.languages,
                                lang.value,
                              ]);
                            } else {
                              handleInputChange(
                                "profile.languages",
                                formData.profile.languages.filter(
                                  (l) => l !== lang.value
                                )
                              );
                            }
                          }}
                          className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                        />
                        <span className="text-sm text-gray-700">
                          {lang.label}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Session Types Offered *
                  </label>
                  <div className="grid grid-cols-2 gap-3">
                    {SESSION_TYPES.map((type) => (
                      <label
                        key={type.value}
                        className="flex items-center space-x-2"
                      >
                        <input
                          type="checkbox"
                          checked={formData.profile.sessionTypes.includes(
                            type.value
                          )}
                          onChange={(e) => {
                            if (e.target.checked) {
                              handleInputChange("profile.sessionTypes", [
                                ...formData.profile.sessionTypes,
                                type.value,
                              ]);
                            } else {
                              handleInputChange(
                                "profile.sessionTypes",
                                formData.profile.sessionTypes.filter(
                                  (t) => t !== type.value
                                )
                              );
                            }
                          }}
                          className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                        />
                        <span className="text-sm text-gray-700">
                          {type.icon} {type.label}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Currency *
                    </label>
                    <select
                      value={formData.pricing.currency}
                      onChange={(e) =>
                        handleInputChange("pricing.currency", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    >
                      <option value="NGN">Nigerian Naira (₦)</option>
                      <option value="USD">US Dollar ($)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Rate per Minute *
                    </label>
                    <input
                      type="number"
                      value={formData.pricing.ratePerMinute}
                      onChange={(e) =>
                        handleInputChange(
                          "pricing.ratePerMinute",
                          parseFloat(e.target.value)
                        )
                      }
                      min="0"
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="0.00"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Session Duration (minutes) *
                    </label>
                    <select
                      value={formData.pricing.minimumSessionDuration}
                      onChange={(e) =>
                        handleInputChange(
                          "pricing.minimumSessionDuration",
                          parseInt(e.target.value)
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    >
                      <option value={30}>30 minutes</option>
                      <option value={45}>45 minutes</option>
                      <option value={60}>60 minutes</option>
                      <option value={90}>90 minutes</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Bank Details */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Bank Details
                </h2>
                <p className="text-gray-600 mb-6">
                  This information is required for payment processing. All
                  details are encrypted and secure.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Account Name *
                    </label>
                    <input
                      type="text"
                      value={formData.bankDetails.accountName}
                      onChange={(e) =>
                        handleInputChange(
                          "bankDetails.accountName",
                          e.target.value
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="Full name as on bank account"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Account Number *
                    </label>
                    <input
                      type="text"
                      value={formData.bankDetails.accountNumber}
                      onChange={(e) =>
                        handleInputChange(
                          "bankDetails.accountNumber",
                          e.target.value
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="10-digit account number"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bank Name *
                    </label>
                    <input
                      type="text"
                      value={formData.bankDetails.bankName}
                      onChange={(e) =>
                        handleInputChange(
                          "bankDetails.bankName",
                          e.target.value
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="e.g., First Bank of Nigeria"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bank Code *
                    </label>
                    <input
                      type="text"
                      value={formData.bankDetails.bankCode}
                      onChange={(e) =>
                        handleInputChange(
                          "bankDetails.bankCode",
                          e.target.value
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="3-digit bank code"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Currency *
                    </label>
                    <select
                      value={formData.bankDetails.currency}
                      onChange={(e) =>
                        handleInputChange(
                          "bankDetails.currency",
                          e.target.value
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    >
                      <option value="NGN">Nigerian Naira (₦)</option>
                      <option value="USD">US Dollar ($)</option>
                    </select>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-blue-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-blue-800">
                        Security Notice
                      </h3>
                      <div className="mt-2 text-sm text-blue-700">
                        <p>
                          Your bank details are encrypted and stored securely.
                          They will only be used for payment processing and will
                          never be shared with third parties.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-red-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6">
              <button
                type="button"
                onClick={prevStep}
                disabled={currentStep === 1}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>

              {currentStep < 4 ? (
                <button
                  type="button"
                  onClick={nextStep}
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? "Submitting..." : "Submit Application"}
                </button>
              )}
            </div>
          </form>
        </div>
      </main>
    </div>
  );
}
