import { Router } from "express";
import { UserController } from "@/controllers/userController";
import { PreferencesController } from "@/controllers/preferencesController";
import { userValidations } from "@/utils/validation";
import {
  authenticate,
  requireAdmin,
  requireSuperAdmin,
} from "@/middleware/auth";
import { uploadProfilePicture, handleUploadError } from "@/middleware/upload";

const router = Router();

// User profile routes
router.get("/profile", authenticate, UserController.getProfile);

router.put(
  "/profile",
  authenticate,
  userValidations.updateProfile,
  UserController.updateProfile
);

router.post(
  "/profile/picture",
  authenticate,
  uploadProfilePicture,
  handleUploadError,
  UserController.uploadProfilePicture
);

router.delete(
  "/profile/picture",
  authenticate,
  UserController.deleteProfilePicture
);

// User preferences routes
router.get("/preferences", authenticate, PreferencesController.getPreferences);

router.put(
  "/preferences",
  authenticate,
  PreferencesController.updateAllPreferences
);

router.put(
  "/preferences/notifications",
  authenticate,
  PreferencesController.updateNotificationPreferences
);

router.put(
  "/preferences/privacy",
  authenticate,
  PreferencesController.updatePrivacyPreferences
);

router.post(
  "/preferences/reset",
  authenticate,
  PreferencesController.resetPreferences
);

router.get(
  "/preferences/export",
  authenticate,
  PreferencesController.exportPreferences
);

router.post(
  "/preferences/import",
  authenticate,
  PreferencesController.importPreferences
);

// Account management
router.post("/deactivate", authenticate, UserController.deactivateAccount);

router.delete("/account/:userId?", authenticate, UserController.deleteAccount);

// Public user info
router.get("/:userId", UserController.getUserById);

// Admin routes
router.get("/", authenticate, requireAdmin, UserController.getUsers);

router.get(
  "/stats/overview",
  authenticate,
  requireAdmin,
  UserController.getUserStats
);

router.put(
  "/:userId/reactivate",
  authenticate,
  requireAdmin,
  UserController.reactivateAccount
);

router.put(
  "/:userId/role",
  authenticate,
  requireAdmin,
  UserController.updateUserRole
);

router.post(
  "/bulk-operations",
  authenticate,
  requireSuperAdmin,
  UserController.bulkUserOperations
);

export default router;
