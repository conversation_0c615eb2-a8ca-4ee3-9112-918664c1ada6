import { Router } from 'express';
import { VideoController } from '@/controllers/videoController';
import { sessionValidations } from '@/utils/validation';
import { 
  authenticate, 
  requireCounselor 
} from '@/middleware/auth';

const router = Router();

// Session management
router.post('/:sessionId/start',
  authenticate,
  VideoController.startSession
);

router.put('/:sessionId/end',
  authenticate,
  VideoController.endSession
);

router.put('/:sessionId/no-show',
  authenticate,
  requireCounselor,
  VideoController.markNoShow
);

// Video room management
router.post('/:sessionId/room',
  authenticate,
  VideoController.createRoom
);

router.get('/:sessionId/token',
  authenticate,
  VideoController.generateToken
);

// Recording management
router.post('/:sessionId/recording/start',
  authenticate,
  requireCounselor,
  VideoController.startRecording
);

router.post('/:sessionId/recording/stop',
  authenticate,
  requireCounselor,
  VideoController.stopRecording
);

router.get('/:sessionId/recordings',
  authenticate,
  VideoController.getRecordings
);

// Analytics and monitoring
router.get('/:sessionId/analytics',
  authenticate,
  VideoController.getRoomAnalytics
);

// Feedback and statistics
router.post('/:sessionId/feedback',
  authenticate,
  sessionValidations.feedback,
  VideoController.submitFeedback
);

router.get('/stats',
  authenticate,
  VideoController.getSessionStats
);

router.get('/upcoming',
  authenticate,
  VideoController.getUpcomingSessions
);

// Webhook endpoint
router.post('/webhook/daily',
  VideoController.handleDailyWebhook
);

export default router;
