// Authentication store using Zustand
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthState, User, AuthTokens, LoginFormData, RegisterFormData } from '@/types/auth';
import { authAPI } from '@/lib/auth';

interface AuthActions {
  // Authentication actions
  login: (credentials: LoginFormData) => Promise<void>;
  register: (data: RegisterFormData) => Promise<void>;
  logout: () => Promise<void>;
  generateGuestToken: () => Promise<void>;
  refreshToken: () => Promise<void>;
  
  // User profile actions
  updateUser: (user: User) => void;
  checkAuth: () => Promise<void>;
  
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Token management
  setTokens: (tokens: AuthTokens) => void;
  clearTokens: () => void;
  
  // Guest mode
  setGuestMode: (guestToken: string) => void;
  clearGuestMode: () => void;
}

type AuthStore = AuthState & AuthActions;

const initialState: AuthState = {
  user: null,
  tokens: null,
  isAuthenticated: false,
  isGuest: false,
  guestToken: null,
  isLoading: false,
  error: null,
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Authentication actions
      login: async (credentials: LoginFormData) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authAPI.login(credentials);
          const { user, tokens } = response.data;
          
          set({
            user,
            tokens,
            isAuthenticated: true,
            isGuest: false,
            guestToken: null,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          });
          throw error;
        }
      },

      register: async (data: RegisterFormData) => {
        set({ isLoading: true, error: null });
        
        try {
          const { confirmPassword, ...registerData } = data;
          const response = await authAPI.register(registerData);
          const { user, tokens } = response.data;
          
          set({
            user,
            tokens,
            isAuthenticated: true,
            isGuest: false,
            guestToken: null,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Registration failed',
          });
          throw error;
        }
      },

      logout: async () => {
        const { tokens } = get();
        
        try {
          if (tokens?.accessToken) {
            await authAPI.logout(tokens.accessToken);
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            ...initialState,
          });
        }
      },

      generateGuestToken: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authAPI.generateGuestToken();
          const { token } = response.data;
          
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isGuest: true,
            guestToken: token,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to generate guest token',
          });
          throw error;
        }
      },

      refreshToken: async () => {
        const { tokens } = get();
        
        if (!tokens?.refreshToken) {
          throw new Error('No refresh token available');
        }
        
        try {
          const response = await authAPI.refreshToken(tokens.refreshToken);
          const { user, tokens: newTokens } = response.data;
          
          set({
            user,
            tokens: newTokens,
            isAuthenticated: true,
          });
        } catch (error) {
          // If refresh fails, logout user
          set({ ...initialState });
          throw error;
        }
      },

      // User profile actions
      updateUser: (user: User) => {
        set({ user });
      },

      checkAuth: async () => {
        const { tokens, guestToken } = get();
        
        try {
          const token = tokens?.accessToken || guestToken;
          const response = await authAPI.checkAuth(token);
          
          if (response.success && response.data) {
            set({
              user: response.data,
              isAuthenticated: true,
              isGuest: false,
            });
          } else if (guestToken) {
            set({
              isGuest: true,
              isAuthenticated: false,
            });
          } else {
            set({ ...initialState });
          }
        } catch (error) {
          set({ ...initialState });
        }
      },

      // State management
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      // Token management
      setTokens: (tokens: AuthTokens) => {
        set({ tokens, isAuthenticated: true });
      },

      clearTokens: () => {
        set({ tokens: null, isAuthenticated: false });
      },

      // Guest mode
      setGuestMode: (guestToken: string) => {
        set({
          guestToken,
          isGuest: true,
          isAuthenticated: false,
          user: null,
          tokens: null,
        });
      },

      clearGuestMode: () => {
        set({
          guestToken: null,
          isGuest: false,
        });
      },
    }),
    {
      name: 'theramea-auth',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
        isGuest: state.isGuest,
        guestToken: state.guestToken,
      }),
    }
  )
);
