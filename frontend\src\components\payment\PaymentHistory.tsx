'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/store/authStore';
import { paymentAPI, PaymentUtils } from '@/lib/payment';
import { PaymentHistory as PaymentHistoryType } from '@/types/payment';

export default function PaymentHistory() {
  const { tokens } = useAuthStore();
  const [payments, setPayments] = useState<PaymentHistoryType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const token = tokens?.accessToken;

  useEffect(() => {
    if (token) {
      fetchPaymentHistory();
    }
  }, [token, currentPage]);

  const fetchPaymentHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await paymentAPI.getPaymentHistory(currentPage, 10, token!);
      setPayments(response.data.payments);
      setTotalPages(response.data.pagination.pages);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load payment history');
    } finally {
      setLoading(false);
    }
  };

  const handleRefundRequest = async (sessionId: string) => {
    if (!token) return;

    const reason = prompt('Please provide a reason for the refund request:');
    if (!reason) return;

    try {
      await paymentAPI.requestRefund(sessionId, reason, token);
      alert('Refund request submitted successfully. We will review it within 24 hours.');
      fetchPaymentHistory(); // Refresh the list
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to request refund');
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return PaymentUtils.formatCurrency(amount, currency as 'NGN' | 'USD');
  };

  const getStatusColor = (status: string) => {
    return PaymentUtils.getPaymentStatusColor(status);
  };

  const getStatusIcon = (status: string) => {
    return PaymentUtils.getPaymentStatusIcon(status);
  };

  if (loading && payments.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (payments.length === 0) {
    return (
      <div className="text-center py-8">
        <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Payment History</h3>
        <p className="text-gray-600">You haven't made any payments yet.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Payment List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Payment History</h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {payments.map((payment) => (
            <div key={payment.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Counselor Avatar */}
                  <div className="flex-shrink-0">
                    {payment.counselor?.profilePicture ? (
                      <img
                        src={payment.counselor.profilePicture}
                        alt={payment.counselor.name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                        <span className="text-lg font-semibold text-purple-600">
                          {payment.counselor?.name?.charAt(0) || 'C'}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Payment Details */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="text-lg font-medium text-gray-900">
                        {formatCurrency(payment.amount, payment.currency)}
                      </h3>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                        {getStatusIcon(payment.status)} {payment.status}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-1">
                      Session with {payment.counselor?.name || 'Unknown Counselor'}
                    </p>
                    
                    {payment.session && (
                      <p className="text-sm text-gray-500">
                        {new Date(payment.session.scheduledAt).toLocaleDateString()} • {payment.session.duration} minutes • {payment.session.type}
                      </p>
                    )}
                    
                    <p className="text-xs text-gray-400 mt-1">
                      Paid on {new Date(payment.paid_at || payment.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-3">
                  {payment.status === 'completed' && !payment.refund && payment.session && (
                    <button
                      onClick={() => handleRefundRequest(payment.session!.id)}
                      className="text-sm text-gray-600 hover:text-gray-800"
                    >
                      Request Refund
                    </button>
                  )}
                  
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      Ref: {payment.reference}
                    </p>
                    {payment.refund && (
                      <p className="text-xs text-blue-600">
                        Refunded: {formatCurrency(payment.refund.amount, payment.currency)}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Refund Status */}
              {payment.refund && (
                <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-900">
                        Refund Status: {payment.refund.status}
                      </p>
                      {payment.refund.processed_at && (
                        <p className="text-xs text-blue-700">
                          Processed on {new Date(payment.refund.processed_at).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                    <span className="text-sm font-medium text-blue-900">
                      {formatCurrency(payment.refund.amount, payment.currency)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Page <span className="font-medium">{currentPage}</span> of{' '}
                  <span className="font-medium">{totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
