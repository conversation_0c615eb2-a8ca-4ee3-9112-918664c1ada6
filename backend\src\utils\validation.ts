import { body, param, query } from "express-validator";
import mongoose from "mongoose";

// Common validation rules
export const commonValidations = {
  // MongoDB ObjectId validation
  objectId: (field: string) =>
    param(field).custom((value) => {
      if (!mongoose.Types.ObjectId.isValid(value)) {
        throw new Error(`Invalid ${field} format`);
      }
      return true;
    }),

  // Email validation
  email: body("email")
    .isEmail()
    .normalizeEmail()
    .withMessage("Please provide a valid email address"),

  // Password validation
  password: body("password")
    .isLength({ min: 8 })
    .withMessage("Password must be at least 8 characters long")
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      "Password must contain at least one lowercase letter, one uppercase letter, and one number"
    ),

  // Username validation
  username: body("username")
    .isLength({ min: 3, max: 30 })
    .withMessage("Username must be between 3 and 30 characters")
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage("Username can only contain letters, numbers, and underscores"),

  // Name validation
  firstName: body("firstName")
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage("First name is required and must be less than 50 characters"),

  lastName: body("lastName")
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage("Last name is required and must be less than 50 characters"),

  // Pagination validation
  page: query("page")
    .optional()
    .isInt({ min: 1 })
    .withMessage("Page must be a positive integer"),

  limit: query("limit")
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage("Limit must be between 1 and 100"),

  // Date validation
  dateField: (field: string) =>
    body(field)
      .optional()
      .isISO8601()
      .withMessage(`${field} must be a valid date`),

  // Rating validation
  rating: body("rating")
    .isInt({ min: 1, max: 5 })
    .withMessage("Rating must be between 1 and 5"),

  // Duration validation (for sessions)
  duration: body("duration")
    .isInt({ min: 15, max: 180 })
    .withMessage("Duration must be between 15 and 180 minutes"),
};

// User validation rules
export const userValidations = {
  register: [
    commonValidations.firstName,
    commonValidations.lastName,
    commonValidations.username,
    commonValidations.email,
    commonValidations.password,
    body("areasOfInterest")
      .optional()
      .isArray()
      .withMessage("Areas of interest must be an array"),
    body("areasOfInterest.*")
      .optional()
      .isIn([
        "relationships",
        "school",
        "anxiety",
        "addiction",
        "stress",
        "mental-health",
        "depression",
        "family",
        "career",
        "self-discovery",
        "grief",
        "trauma",
        "anger-management",
        "eating-disorders",
        "sleep-issues",
      ])
      .withMessage("Invalid area of interest"),
  ],

  login: [
    body("email")
      .isEmail()
      .normalizeEmail()
      .withMessage("Please provide a valid email"),
    body("password").notEmpty().withMessage("Password is required"),
  ],

  updateProfile: [
    body("firstName").optional().trim().isLength({ min: 1, max: 50 }),
    body("lastName").optional().trim().isLength({ min: 1, max: 50 }),
    body("areasOfInterest").optional().isArray(),
    body("location.country").optional().isString().isLength({ max: 100 }),
    body("location.city").optional().isString().isLength({ max: 100 }),
  ],
};

// Counselor validation rules
export const counselorValidations = {
  register: [
    body("bio")
      .trim()
      .isLength({ min: 50, max: 1000 })
      .withMessage("Bio must be between 50 and 1000 characters"),

    body("specializations")
      .isArray({ min: 1 })
      .withMessage("At least one specialization is required"),

    body("specializations.*")
      .isIn([
        "anxiety-disorders",
        "depression",
        "trauma-ptsd",
        "relationship-counseling",
        "family-therapy",
        "addiction-recovery",
        "grief-counseling",
        "stress-management",
        "career-counseling",
        "adolescent-therapy",
        "couples-therapy",
        "eating-disorders",
        "anger-management",
        "sleep-disorders",
        "life-coaching",
        "spiritual-counseling",
      ])
      .withMessage("Invalid specialization"),

    body("qualifications")
      .isArray({ min: 1 })
      .withMessage("At least one qualification is required"),

    body("qualifications.*.degree")
      .trim()
      .notEmpty()
      .withMessage("Degree is required"),

    body("qualifications.*.institution")
      .trim()
      .notEmpty()
      .withMessage("Institution is required"),

    body("qualifications.*.year")
      .isInt({ min: 1950, max: new Date().getFullYear() })
      .withMessage("Invalid graduation year"),

    body("experience.years")
      .isInt({ min: 0, max: 50 })
      .withMessage("Experience years must be between 0 and 50"),

    body("experience.description")
      .trim()
      .isLength({ min: 10, max: 500 })
      .withMessage(
        "Experience description must be between 10 and 500 characters"
      ),

    body("pricing.ratePerMinute")
      .isFloat({ min: 1 })
      .withMessage("Rate per minute must be at least 1"),

    body("pricing.currency")
      .isIn(["NGN", "USD"])
      .withMessage("Currency must be NGN or USD"),
  ],

  updateAvailability: [
    body("timezone")
      .optional()
      .isString()
      .withMessage("Timezone must be a valid string"),

    body("schedule")
      .optional()
      .isObject()
      .withMessage("Schedule must be an object"),

    body("unavailableDates")
      .optional()
      .isArray()
      .withMessage("Unavailable dates must be an array"),

    body("unavailableDates.*")
      .optional()
      .isISO8601()
      .withMessage("Each unavailable date must be a valid date"),
  ],
};

// Session validation rules
export const sessionValidations = {
  book: [
    body("counselorId")
      .custom((value) => mongoose.Types.ObjectId.isValid(value))
      .withMessage("Invalid counselor ID"),

    body("scheduledAt")
      .isISO8601()
      .custom((value) => {
        const scheduledDate = new Date(value);
        const now = new Date();
        if (scheduledDate <= now) {
          throw new Error("Session must be scheduled for a future date");
        }
        return true;
      })
      .withMessage("Invalid scheduled date"),

    commonValidations.duration,

    body("type")
      .optional()
      .isIn(["individual", "group", "couples", "family"])
      .withMessage("Invalid session type"),
  ],

  reschedule: [
    commonValidations.objectId("sessionId"),
    body("newScheduledAt")
      .isISO8601()
      .custom((value) => {
        const scheduledDate = new Date(value);
        const now = new Date();
        if (scheduledDate <= now) {
          throw new Error("Session must be rescheduled for a future date");
        }
        return true;
      }),
    body("reason")
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage("Reason cannot exceed 500 characters"),
  ],

  feedback: [
    commonValidations.objectId("sessionId"),
    commonValidations.rating,
    body("comment")
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage("Comment cannot exceed 500 characters"),
    body("wouldRecommend")
      .optional()
      .isBoolean()
      .withMessage("Would recommend must be a boolean"),
  ],
};

// Chat validation rules
export const chatValidations = {
  createRoom: [
    body("name")
      .trim()
      .isLength({ min: 3, max: 100 })
      .withMessage("Room name must be between 3 and 100 characters"),

    body("description")
      .trim()
      .isLength({ min: 10, max: 500 })
      .withMessage("Description must be between 10 and 500 characters"),

    body("topic")
      .isIn([
        "general-support",
        "anxiety-depression",
        "relationships",
        "family-issues",
        "work-stress",
        "student-life",
        "grief-loss",
        "addiction-recovery",
        "self-care",
        "mental-health-awareness",
        "crisis-support",
        "lgbtq-support",
        "parenting",
        "chronic-illness",
        "eating-disorders",
      ])
      .withMessage("Invalid topic"),

    body("category")
      .isIn([
        "support-groups",
        "educational",
        "peer-chat",
        "crisis-support",
        "special-topics",
      ])
      .withMessage("Invalid category"),

    body("maxParticipants")
      .optional()
      .isInt({ min: 2, max: 100 })
      .withMessage("Max participants must be between 2 and 100"),
  ],

  sendMessage: [
    commonValidations.objectId("roomId"),
    body("content.text")
      .trim()
      .isLength({ min: 1, max: 2000 })
      .withMessage("Message must be between 1 and 2000 characters"),

    body("content.type")
      .optional()
      .isIn(["text", "image", "file", "emoji"])
      .withMessage("Invalid message type"),

    body("replyTo")
      .optional()
      .custom((value) => mongoose.Types.ObjectId.isValid(value))
      .withMessage("Invalid reply message ID"),
  ],
};

// Resource validation rules
export const resourceValidations = {
  create: [
    body("title")
      .trim()
      .isLength({ min: 5, max: 200 })
      .withMessage("Title must be between 5 and 200 characters"),

    body("description")
      .trim()
      .isLength({ min: 20, max: 500 })
      .withMessage("Description must be between 20 and 500 characters"),

    body("content")
      .trim()
      .isLength({ min: 100 })
      .withMessage("Content must be at least 100 characters"),

    body("type")
      .isIn(["article", "video", "audio", "tool", "worksheet", "guide"])
      .withMessage("Invalid resource type"),

    body("category")
      .isIn([
        "mental-health-basics",
        "anxiety-management",
        "depression-support",
        "stress-relief",
        "relationship-skills",
        "self-care",
        "mindfulness-meditation",
        "coping-strategies",
        "crisis-resources",
        "workplace-wellness",
        "student-support",
        "parenting-guidance",
        "grief-support",
        "addiction-recovery",
        "trauma-healing",
      ])
      .withMessage("Invalid category"),

    body("difficulty")
      .optional()
      .isIn(["beginner", "intermediate", "advanced"])
      .withMessage("Invalid difficulty level"),

    body("tags").optional().isArray().withMessage("Tags must be an array"),

    body("estimatedReadTime")
      .optional()
      .isInt({ min: 1, max: 120 })
      .withMessage("Estimated read time must be between 1 and 120 minutes"),
  ],
};

export default {
  commonValidations,
  userValidations,
  counselorValidations,
  sessionValidations,
  chatValidations,
  resourceValidations,
};
