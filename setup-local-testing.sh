#!/bin/bash

# 🧪 Theramea Local Testing Setup Script

echo "🚀 Setting up Theramea for local testing..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node -v)"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    print_success "Node.js $(node -v) and npm $(npm -v) are installed"
}

# Check if Docker is available for databases
check_docker() {
    if command -v docker &> /dev/null; then
        print_success "Docker is available for database setup"
        return 0
    else
        print_warning "Docker not found. You'll need to install MongoDB and Redis manually"
        return 1
    fi
}

# Setup databases with Docker
setup_databases() {
    if check_docker; then
        print_status "Setting up databases with Docker..."
        
        # Start MongoDB
        if ! docker ps | grep -q mongodb; then
            print_status "Starting MongoDB container..."
            docker run -d -p 27017:27017 --name mongodb mongo:latest
            if [ $? -eq 0 ]; then
                print_success "MongoDB started on port 27017"
            else
                print_error "Failed to start MongoDB"
            fi
        else
            print_success "MongoDB is already running"
        fi
        
        # Start Redis
        if ! docker ps | grep -q redis; then
            print_status "Starting Redis container..."
            docker run -d -p 6379:6379 --name redis redis:alpine
            if [ $? -eq 0 ]; then
                print_success "Redis started on port 6379"
            else
                print_error "Failed to start Redis"
            fi
        else
            print_success "Redis is already running"
        fi
        
        # Wait for databases to be ready
        print_status "Waiting for databases to be ready..."
        sleep 5
    else
        print_warning "Please ensure MongoDB and Redis are running manually"
    fi
}

# Create environment files
create_env_files() {
    print_status "Creating environment files..."
    
    # Backend .env
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend/.env file..."
        cat > backend/.env << EOF
# Database
MONGODB_URI=mongodb://localhost:27017/theramea_dev
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-super-secret-jwt-key-for-development-$(date +%s)
JWT_REFRESH_SECRET=your-super-secret-refresh-key-for-development-$(date +%s)

# Email (for testing - use Mailtrap or similar)
EMAIL_HOST=smtp.mailtrap.io
EMAIL_PORT=2525
EMAIL_USER=your-mailtrap-username
EMAIL_PASS=your-mailtrap-password
EMAIL_FROM=<EMAIL>

# Paystack (use test keys)
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key

# Daily.co (use test domain)
DAILY_API_KEY=your_daily_api_key
DAILY_DOMAIN=your-test-domain.daily.co

# File Upload
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# Server
PORT=5000
NODE_ENV=development
EOF
        print_success "Created backend/.env"
    else
        print_warning "backend/.env already exists"
    fi
    
    # Frontend .env.local
    if [ ! -f "frontend/.env.local" ]; then
        print_status "Creating frontend/.env.local file..."
        cat > frontend/.env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_SOCKET_URL=http://localhost:5000
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key
NEXT_PUBLIC_DAILY_DOMAIN=your-test-domain.daily.co
EOF
        print_success "Created frontend/.env.local"
    else
        print_warning "frontend/.env.local already exists"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Backend dependencies
    if [ -d "backend" ]; then
        print_status "Installing backend dependencies..."
        cd backend
        npm install
        if [ $? -eq 0 ]; then
            print_success "Backend dependencies installed"
        else
            print_error "Failed to install backend dependencies"
            exit 1
        fi
        cd ..
    else
        print_error "Backend directory not found"
        exit 1
    fi
    
    # Frontend dependencies
    if [ -d "frontend" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend
        npm install
        if [ $? -eq 0 ]; then
            print_success "Frontend dependencies installed"
        else
            print_error "Failed to install frontend dependencies"
            exit 1
        fi
        cd ..
    else
        print_error "Frontend directory not found"
        exit 1
    fi
}

# Create uploads directory
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p backend/uploads
    mkdir -p backend/logs
    
    print_success "Directories created"
}

# Test database connections
test_connections() {
    print_status "Testing database connections..."
    
    # Test MongoDB
    if command -v mongosh &> /dev/null; then
        mongosh --eval "db.runCommand('ping')" mongodb://localhost:27017/theramea_dev &> /dev/null
        if [ $? -eq 0 ]; then
            print_success "MongoDB connection successful"
        else
            print_error "MongoDB connection failed"
        fi
    else
        print_warning "mongosh not found, skipping MongoDB test"
    fi
    
    # Test Redis
    if command -v redis-cli &> /dev/null; then
        redis-cli ping &> /dev/null
        if [ $? -eq 0 ]; then
            print_success "Redis connection successful"
        else
            print_error "Redis connection failed"
        fi
    else
        print_warning "redis-cli not found, skipping Redis test"
    fi
}

# Create test data
create_test_data() {
    print_status "Would you like to create test data? (y/n)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Creating test data..."
        
        # This would typically run a script to create test users, counselors, etc.
        # For now, we'll just create the script
        cat > create-test-data.js << 'EOF'
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/theramea_dev');

// Create test user
async function createTestData() {
    try {
        // Create test user
        const hashedPassword = await bcrypt.hash('password123', 10);
        
        const testUser = {
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            password: hashedPassword,
            role: 'user',
            isActive: true,
            isEmailVerified: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        // Insert test data (you'll need to implement the actual models)
        console.log('Test data creation script ready');
        console.log('Run this script after starting the backend server');
        
    } catch (error) {
        console.error('Error creating test data:', error);
    } finally {
        mongoose.disconnect();
    }
}

createTestData();
EOF
        
        print_success "Test data script created (create-test-data.js)"
    fi
}

# Main setup function
main() {
    echo "🧪 Theramea Local Testing Setup"
    echo "================================"
    
    check_requirements
    setup_databases
    create_env_files
    install_dependencies
    create_directories
    test_connections
    create_test_data
    
    echo ""
    echo "🎉 Setup complete!"
    echo ""
    echo "Next steps:"
    echo "1. Update the API keys in the .env files:"
    echo "   - Paystack test keys (get from https://dashboard.paystack.com/)"
    echo "   - Daily.co API key (get from https://dashboard.daily.co/)"
    echo "   - Email service credentials (Mailtrap recommended for testing)"
    echo ""
    echo "2. Start the servers:"
    echo "   Terminal 1: cd backend && npm run dev"
    echo "   Terminal 2: cd frontend && npm run dev"
    echo ""
    echo "3. Open http://localhost:3000 in your browser"
    echo ""
    echo "4. Follow the LOCAL_TESTING_GUIDE.md for comprehensive testing"
    echo ""
    echo "Happy testing! 🚀"
}

# Run main function
main
