import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { ContentService } from '@/services/contentService';
import { SearchService } from '@/services/searchService';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import { validateFile, FILE_TYPES, FILE_SIZE_LIMITS } from '@/utils/cloudinary';

export class ContentController {
  /**
   * Create new content
   */
  static async createContent(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400));
      }

      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      // Check if user can create content (admin or counselor)
      if (req.user.role !== 'admin' && req.user.role !== 'counselor') {
        return next(createError('Insufficient permissions to create content', 403));
      }

      const content = await ContentService.createContent(req.user._id.toString(), req.body);

      res.status(201).json({
        success: true,
        message: 'Content created successfully',
        data: { content }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get content list with filters
   */
  static async getContent(req: Request, res: Response, next: NextFunction) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'publishedAt',
        sortOrder = 'desc',
        type,
        category,
        subcategory,
        tags,
        difficulty,
        targetAudience,
        mentalHealthTopics,
        isPremium,
        search,
        minRating
      } = req.query;

      const filters: any = {
        isPublished: true // Only show published content to public
      };

      // Admin and content creators can see unpublished content
      if (req.user && (req.user.role === 'admin' || req.user.role === 'counselor')) {
        delete filters.isPublished;
      }

      if (type) filters.type = Array.isArray(type) ? type : [type];
      if (category) filters.category = category;
      if (subcategory) filters.subcategory = subcategory;
      if (tags) filters.tags = Array.isArray(tags) ? tags : [tags];
      if (difficulty) filters.difficulty = Array.isArray(difficulty) ? difficulty : [difficulty];
      if (targetAudience) filters.targetAudience = Array.isArray(targetAudience) ? targetAudience : [targetAudience];
      if (mentalHealthTopics) filters.mentalHealthTopics = Array.isArray(mentalHealthTopics) ? mentalHealthTopics : [mentalHealthTopics];
      if (isPremium !== undefined) filters.isPremium = isPremium === 'true';
      if (search) filters.search = search;
      if (minRating) filters.minRating = parseFloat(minRating as string);

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc'
      };

      const result = await ContentService.getContent(filters, options);

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get specific content by ID or slug
   */
  static async getContentById(req: Request, res: Response, next: NextFunction) {
    try {
      const { identifier } = req.params;
      const content = await ContentService.getContentById(identifier);

      // Check if content is published or user has permission to view
      if (!content.isPublished && (!req.user || (req.user.role !== 'admin' && req.user.role !== 'counselor'))) {
        return next(createError('Content not found', 404));
      }

      // Track view if user is authenticated
      if (req.user) {
        await ContentService.trackInteraction(content._id.toString(), {
          userId: req.user._id.toString(),
          action: 'view'
        });
      }

      res.json({
        success: true,
        data: { content }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update content
   */
  static async updateContent(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400));
      }

      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { contentId } = req.params;
      const content = await ContentService.getContentById(contentId);

      // Check permissions
      const isOwner = content.author._id.toString() === req.user._id.toString();
      const isAdmin = req.user.role === 'admin';

      if (!isOwner && !isAdmin) {
        return next(createError('Insufficient permissions to update this content', 403));
      }

      const updatedContent = await ContentService.updateContent(contentId, req.body);

      res.json({
        success: true,
        message: 'Content updated successfully',
        data: { content: updatedContent }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete content
   */
  static async deleteContent(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { contentId } = req.params;
      const content = await ContentService.getContentById(contentId);

      // Check permissions
      const isOwner = content.author._id.toString() === req.user._id.toString();
      const isAdmin = req.user.role === 'admin';

      if (!isOwner && !isAdmin) {
        return next(createError('Insufficient permissions to delete this content', 403));
      }

      await ContentService.deleteContent(contentId);

      res.json({
        success: true,
        message: 'Content deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Search content
   */
  static async searchContent(req: Request, res: Response, next: NextFunction) {
    try {
      const {
        q: query,
        page = 1,
        limit = 20,
        sortBy = 'relevance',
        sortOrder = 'desc',
        type,
        category,
        difficulty,
        tags,
        mentalHealthTopics,
        isPremium,
        minRating
      } = req.query;

      if (!query) {
        return next(createError('Search query is required', 400));
      }

      const searchQuery = {
        query: query as string,
        filters: {
          type: type ? (Array.isArray(type) ? type : [type]) : undefined,
          category: category as string,
          difficulty: difficulty ? (Array.isArray(difficulty) ? difficulty : [difficulty]) : undefined,
          tags: tags ? (Array.isArray(tags) ? tags : [tags]) : undefined,
          mentalHealthTopics: mentalHealthTopics ? (Array.isArray(mentalHealthTopics) ? mentalHealthTopics : [mentalHealthTopics]) : undefined,
          isPremium: isPremium !== undefined ? isPremium === 'true' : undefined,
          minRating: minRating ? parseFloat(minRating as string) : undefined
        },
        options: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          sortBy: sortBy as string,
          sortOrder: sortOrder as 'asc' | 'desc'
        }
      };

      const result = await SearchService.search(searchQuery);

      // Track search
      await SearchService.trackSearch(
        query as string,
        req.user?._id.toString(),
        result.pagination.total
      );

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get autocomplete suggestions
   */
  static async getAutocomplete(req: Request, res: Response, next: NextFunction) {
    try {
      const { q: query, limit = 10 } = req.query;

      if (!query) {
        return res.json({
          success: true,
          data: {
            suggestions: [],
            categories: [],
            tags: [],
            topics: []
          }
        });
      }

      const result = await SearchService.getAutocomplete(
        query as string,
        parseInt(limit as string)
      );

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Like/unlike content
   */
  static async toggleLike(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { contentId } = req.params;
      
      await ContentService.trackInteraction(contentId, {
        userId: req.user._id.toString(),
        action: 'like'
      });

      res.json({
        success: true,
        message: 'Content liked successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Bookmark/unbookmark content
   */
  static async toggleBookmark(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { contentId } = req.params;
      
      await ContentService.trackInteraction(contentId, {
        userId: req.user._id.toString(),
        action: 'bookmark'
      });

      res.json({
        success: true,
        message: 'Content bookmarked successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Rate content
   */
  static async rateContent(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { contentId } = req.params;
      const { rating, review } = req.body;

      if (!rating || rating < 1 || rating > 5) {
        return next(createError('Rating must be between 1 and 5', 400));
      }

      const content = await ContentService.addRating(
        contentId,
        req.user._id.toString(),
        rating,
        review
      );

      res.json({
        success: true,
        message: 'Rating submitted successfully',
        data: { content }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user bookmarks
   */
  static async getUserBookmarks(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const {
        page = 1,
        limit = 20,
        type,
        category
      } = req.query;

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        type: type as string,
        category: category as string
      };

      const result = await ContentService.getUserBookmarks(req.user._id.toString(), options);

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get content recommendations
   */
  static async getRecommendations(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { limit = 10 } = req.query;

      const recommendations = await ContentService.getRecommendations(
        req.user._id.toString(),
        parseInt(limit as string)
      );

      res.json({
        success: true,
        data: { recommendations }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get trending content
   */
  static async getTrendingContent(req: Request, res: Response, next: NextFunction) {
    try {
      const { timeframe = 'week', limit = 10 } = req.query;

      const trending = await ContentService.getTrendingContent(
        timeframe as 'day' | 'week' | 'month',
        parseInt(limit as string)
      );

      res.json({
        success: true,
        data: { trending }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get similar content
   */
  static async getSimilarContent(req: Request, res: Response, next: NextFunction) {
    try {
      const { contentId } = req.params;
      const { limit = 5 } = req.query;

      const similar = await SearchService.getSimilarContent(
        contentId,
        parseInt(limit as string)
      );

      res.json({
        success: true,
        data: { similar }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Upload content media
   */
  static async uploadMedia(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      if (!req.file) {
        return next(createError('File is required', 400));
      }

      const { type } = req.body;

      if (!type || !['thumbnail', 'video', 'audio', 'document'].includes(type)) {
        return next(createError('Valid media type is required', 400));
      }

      // Validate file based on type
      let allowedTypes: string[];
      let maxSize: number;

      switch (type) {
        case 'thumbnail':
          allowedTypes = FILE_TYPES.IMAGES;
          maxSize = FILE_SIZE_LIMITS.IMAGE;
          break;
        case 'video':
          allowedTypes = FILE_TYPES.VIDEOS;
          maxSize = FILE_SIZE_LIMITS.VIDEO;
          break;
        case 'audio':
          allowedTypes = FILE_TYPES.AUDIO;
          maxSize = FILE_SIZE_LIMITS.AUDIO;
          break;
        case 'document':
          allowedTypes = FILE_TYPES.DOCUMENTS;
          maxSize = FILE_SIZE_LIMITS.DOCUMENT;
          break;
        default:
          return next(createError('Invalid media type', 400));
      }

      validateFile(req.file, allowedTypes, maxSize);

      const url = await ContentService.uploadMedia(req.file, type);

      res.json({
        success: true,
        message: 'Media uploaded successfully',
        data: {
          url,
          type,
          fileName: req.file.originalname,
          fileSize: req.file.size
        }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get content categories
   */
  static async getCategories(req: Request, res: Response, next: NextFunction) {
    try {
      const categories = await ContentService.getCategories();

      res.json({
        success: true,
        data: { categories }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get popular tags
   */
  static async getPopularTags(req: Request, res: Response, next: NextFunction) {
    try {
      const { limit = 20 } = req.query;

      const tags = await ContentService.getPopularTags(parseInt(limit as string));

      res.json({
        success: true,
        data: { tags }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get popular searches
   */
  static async getPopularSearches(req: Request, res: Response, next: NextFunction) {
    try {
      const { limit = 10 } = req.query;

      const searches = await SearchService.getPopularSearches(parseInt(limit as string));

      res.json({
        success: true,
        data: { searches }
      });
    } catch (error) {
      next(error);
    }
  }
}
