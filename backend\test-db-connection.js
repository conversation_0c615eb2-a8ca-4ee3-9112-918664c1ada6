const mongoose = require('mongoose');
const { createClient } = require('redis');
require('dotenv').config({ path: '../.env' });

async function testConnections() {
  console.log('Testing database connections...');
  
  // Test MongoDB
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/theramea';
    console.log('Attempting to connect to MongoDB:', mongoURI);
    
    await mongoose.connect(mongoURI, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferCommands: false,
    });
    
    console.log('✓ MongoDB connection successful');
    await mongoose.connection.close();
    console.log('✓ MongoDB connection closed');
  } catch (error) {
    console.log('✗ MongoDB connection failed:', error.message);
  }
  
  // Test Redis
  try {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    console.log('Attempting to connect to Redis:', redisUrl);
    
    const redisClient = createClient({
      url: redisUrl,
      password: process.env.REDIS_PASSWORD || undefined,
    });
    
    await redisClient.connect();
    console.log('✓ Redis connection successful');
    
    await redisClient.quit();
    console.log('✓ Redis connection closed');
  } catch (error) {
    console.log('✗ Redis connection failed:', error.message);
  }
  
  console.log('Database connection tests completed.');
}

testConnections().catch(console.error);
