import { Router } from "express";
import { BookingController } from "@/controllers/bookingController";
import { sessionValidations } from "@/utils/validation";
import { authenticate, requireCounselor } from "@/middleware/auth";

const router = Router();

// Public routes
router.get("/pricing", BookingController.calculatePricing);

router.get("/banks", BookingController.getSupportedBanks);

// Booking management
router.post(
  "/",
  authenticate,
  sessionValidations.book,
  BookingController.createBooking
);

router.get("/", authenticate, BookingController.getBookings);

router.get("/:sessionId", authenticate, BookingController.getBooking);

// Payment routes
router.post(
  "/:sessionId/payment/initialize",
  authenticate,
  BookingController.initializePayment
);

router.get("/payment/verify/:reference", BookingController.verifyPayment);

router.post("/payment/webhook", BookingController.handlePaymentWebhook);

// Booking actions
router.put("/:sessionId/cancel", authenticate, BookingController.cancelBooking);

router.put(
  "/:sessionId/reschedule",
  authenticate,
  sessionValidations.reschedule,
  BookingController.rescheduleBooking
);

// Counselor actions
router.put(
  "/:sessionId/approve",
  authenticate,
  requireCounselor,
  BookingController.approveBooking
);

router.put(
  "/:sessionId/reject",
  authenticate,
  requireCounselor,
  BookingController.rejectBooking
);

export default router;
