import { Router } from 'express';

const router = Router();

// Placeholder routes - will be implemented in admin panel task
router.get('/counselors/pending', (req, res) => {
  res.json({ message: 'Get pending counselors endpoint - to be implemented' });
});

router.put('/counselors/:id/approve', (req, res) => {
  res.json({ message: 'Approve counselor endpoint - to be implemented' });
});

router.get('/sessions/reports', (req, res) => {
  res.json({ message: 'Get session reports endpoint - to be implemented' });
});

export default router;
