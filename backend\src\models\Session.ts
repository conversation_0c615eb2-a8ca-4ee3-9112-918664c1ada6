import mongoose, { Document, Schema } from 'mongoose';

export interface ISession extends Document {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  counselorId: mongoose.Types.ObjectId;
  scheduledAt: Date;
  duration: number; // in minutes
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled' | 'no-show' | 'rescheduled';
  type: 'individual' | 'group' | 'couples' | 'family';
  pricing: {
    currency: 'NGN' | 'USD';
    ratePerMinute: number;
    totalAmount: number;
    platformFee: number;
    counselorEarnings: number;
  };
  payment: {
    paymentId: string;
    paymentMethod: 'paystack' | 'stripe';
    status: 'pending' | 'completed' | 'failed' | 'refunded' | 'partially-refunded';
    paidAt?: Date;
    refundedAt?: Date;
    refundAmount?: number;
    transactionReference: string;
  };
  videoSession: {
    roomId?: string;
    roomUrl?: string;
    provider: 'daily.co';
    startedAt?: Date;
    endedAt?: Date;
    actualDuration?: number; // actual session duration in minutes
    recordingUrl?: string;
    connectionIssues?: {
      timestamp: Date;
      type: 'audio' | 'video' | 'connection';
      description: string;
      reportedBy: 'user' | 'counselor';
    }[];
  };
  notes: {
    counselorNotes?: string;
    sessionSummary?: string;
    nextSteps?: string;
    isPrivate: boolean;
  };
  feedback: {
    userFeedback?: {
      rating: number; // 1-5
      comment?: string;
      submittedAt: Date;
      wouldRecommend: boolean;
    };
    counselorFeedback?: {
      sessionQuality: number; // 1-5
      clientEngagement: number; // 1-5
      technicalIssues: boolean;
      notes?: string;
      submittedAt: Date;
    };
  };
  reschedule: {
    requestedBy?: 'user' | 'counselor';
    requestedAt?: Date;
    reason?: string;
    newScheduledAt?: Date;
    status?: 'pending' | 'approved' | 'rejected';
    respondedAt?: Date;
  };
  cancellation: {
    cancelledBy?: 'user' | 'counselor' | 'admin';
    cancelledAt?: Date;
    reason?: string;
    refundEligible: boolean;
    refundAmount?: number;
  };
  reminders: {
    sent: boolean;
    sentAt?: Date;
    emailSent: boolean;
    pushSent: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

const sessionSchema = new Schema<ISession>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  counselorId: {
    type: Schema.Types.ObjectId,
    ref: 'Counselor',
    required: true
  },
  scheduledAt: {
    type: Date,
    required: true,
    validate: {
      validator: function(value: Date) {
        return value > new Date();
      },
      message: 'Session must be scheduled for a future date'
    }
  },
  duration: {
    type: Number,
    required: true,
    min: [15, 'Session duration must be at least 15 minutes'],
    max: [180, 'Session duration cannot exceed 180 minutes']
  },
  status: {
    type: String,
    enum: ['scheduled', 'in-progress', 'completed', 'cancelled', 'no-show', 'rescheduled'],
    default: 'scheduled'
  },
  type: {
    type: String,
    enum: ['individual', 'group', 'couples', 'family'],
    default: 'individual'
  },
  pricing: {
    currency: {
      type: String,
      enum: ['NGN', 'USD'],
      required: true
    },
    ratePerMinute: {
      type: Number,
      required: true,
      min: 1
    },
    totalAmount: {
      type: Number,
      required: true,
      min: 0
    },
    platformFee: {
      type: Number,
      required: true,
      min: 0
    },
    counselorEarnings: {
      type: Number,
      required: true,
      min: 0
    }
  },
  payment: {
    paymentId: {
      type: String,
      required: true
    },
    paymentMethod: {
      type: String,
      enum: ['paystack', 'stripe'],
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'refunded', 'partially-refunded'],
      default: 'pending'
    },
    paidAt: Date,
    refundedAt: Date,
    refundAmount: Number,
    transactionReference: {
      type: String,
      required: true
    }
  },
  videoSession: {
    roomId: String,
    roomUrl: String,
    provider: {
      type: String,
      enum: ['daily.co'],
      default: 'daily.co'
    },
    startedAt: Date,
    endedAt: Date,
    actualDuration: Number,
    recordingUrl: String,
    connectionIssues: [{
      timestamp: {
        type: Date,
        default: Date.now
      },
      type: {
        type: String,
        enum: ['audio', 'video', 'connection'],
        required: true
      },
      description: {
        type: String,
        required: true
      },
      reportedBy: {
        type: String,
        enum: ['user', 'counselor'],
        required: true
      }
    }]
  },
  notes: {
    counselorNotes: {
      type: String,
      maxlength: [2000, 'Counselor notes cannot exceed 2000 characters']
    },
    sessionSummary: {
      type: String,
      maxlength: [1000, 'Session summary cannot exceed 1000 characters']
    },
    nextSteps: {
      type: String,
      maxlength: [500, 'Next steps cannot exceed 500 characters']
    },
    isPrivate: {
      type: Boolean,
      default: true
    }
  },
  feedback: {
    userFeedback: {
      rating: {
        type: Number,
        min: 1,
        max: 5
      },
      comment: {
        type: String,
        maxlength: [500, 'Feedback comment cannot exceed 500 characters']
      },
      submittedAt: Date,
      wouldRecommend: Boolean
    },
    counselorFeedback: {
      sessionQuality: {
        type: Number,
        min: 1,
        max: 5
      },
      clientEngagement: {
        type: Number,
        min: 1,
        max: 5
      },
      technicalIssues: {
        type: Boolean,
        default: false
      },
      notes: {
        type: String,
        maxlength: [500, 'Counselor feedback notes cannot exceed 500 characters']
      },
      submittedAt: Date
    }
  },
  reschedule: {
    requestedBy: {
      type: String,
      enum: ['user', 'counselor']
    },
    requestedAt: Date,
    reason: String,
    newScheduledAt: Date,
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected']
    },
    respondedAt: Date
  },
  cancellation: {
    cancelledBy: {
      type: String,
      enum: ['user', 'counselor', 'admin']
    },
    cancelledAt: Date,
    reason: String,
    refundEligible: {
      type: Boolean,
      default: false
    },
    refundAmount: Number
  },
  reminders: {
    sent: {
      type: Boolean,
      default: false
    },
    sentAt: Date,
    emailSent: {
      type: Boolean,
      default: false
    },
    pushSent: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
sessionSchema.index({ userId: 1, scheduledAt: -1 });
sessionSchema.index({ counselorId: 1, scheduledAt: -1 });
sessionSchema.index({ status: 1 });
sessionSchema.index({ scheduledAt: 1 });
sessionSchema.index({ 'payment.status': 1 });
sessionSchema.index({ createdAt: -1 });

// Compound indexes for efficient queries
sessionSchema.index({ userId: 1, status: 1 });
sessionSchema.index({ counselorId: 1, status: 1 });

// Virtual to populate user and counselor details
sessionSchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

sessionSchema.virtual('counselor', {
  ref: 'Counselor',
  localField: 'counselorId',
  foreignField: '_id',
  justOne: true
});

// Pre-save middleware to calculate pricing
sessionSchema.pre('save', function(next) {
  if (this.isModified('duration') || this.isModified('pricing.ratePerMinute')) {
    const totalAmount = this.duration * this.pricing.ratePerMinute;
    const platformFeePercentage = 0.1; // 10% platform fee
    const platformFee = totalAmount * platformFeePercentage;
    
    this.pricing.totalAmount = totalAmount;
    this.pricing.platformFee = platformFee;
    this.pricing.counselorEarnings = totalAmount - platformFee;
  }
  next();
});

export const Session = mongoose.model<ISession>('Session', sessionSchema);
