'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { videoAPI, DailyUtils } from '@/lib/video';
import { 
  VideoCallProps, 
  CallState, 
  Participant, 
  ChatMessage, 
  DailyCallObject,
  VideoError 
} from '@/types/video';

export default function VideoCall({
  sessionId,
  token,
  roomUrl,
  userRole,
  userName,
  onCallEnd,
  onError
}: VideoCallProps) {
  const [callObject, setCallObject] = useState<DailyCallObject | null>(null);
  const [callState, setCallState] = useState<CallState>({
    isJoined: false,
    isConnecting: false,
    participants: [],
    isRecording: false,
    connectionQuality: 'good',
    hasAudio: true,
    hasVideo: true,
    isScreenSharing: false,
    chatMessages: [],
    callDuration: 0,
  });
  
  const videoRef = useRef<HTMLDivElement>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout>();
  const callStartTimeRef = useRef<Date>();

  // Initialize call object
  useEffect(() => {
    const initializeCall = async () => {
      try {
        const daily = await DailyUtils.createCallObject();
        setCallObject(daily);
        
        // Set up event listeners
        daily.on('joined-meeting', handleJoinedMeeting);
        daily.on('left-meeting', handleLeftMeeting);
        daily.on('participant-joined', handleParticipantJoined);
        daily.on('participant-left', handleParticipantLeft);
        daily.on('participant-updated', handleParticipantUpdated);
        daily.on('recording-started', handleRecordingStarted);
        daily.on('recording-stopped', handleRecordingStopped);
        daily.on('app-message', handleChatMessage);
        daily.on('error', handleError);
        daily.on('network-quality-change', handleNetworkQualityChange);
        
      } catch (error) {
        handleError({ error: { msg: 'Failed to initialize video call' } });
      }
    };

    initializeCall();

    return () => {
      if (callObject) {
        callObject.destroy();
      }
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    };
  }, []);

  // Event handlers
  const handleJoinedMeeting = useCallback((event: any) => {
    setCallState(prev => ({
      ...prev,
      isJoined: true,
      isConnecting: false,
      localParticipant: event.participants.local,
    }));
    
    callStartTimeRef.current = new Date();
    
    // Start duration timer
    durationIntervalRef.current = setInterval(() => {
      if (callStartTimeRef.current) {
        const duration = Math.floor((Date.now() - callStartTimeRef.current.getTime()) / 1000);
        setCallState(prev => ({ ...prev, callDuration: duration }));
      }
    }, 1000);
  }, []);

  const handleLeftMeeting = useCallback(() => {
    setCallState(prev => ({
      ...prev,
      isJoined: false,
      participants: [],
    }));
    
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
    }
    
    onCallEnd?.();
  }, [onCallEnd]);

  const handleParticipantJoined = useCallback((event: any) => {
    const participant: Participant = {
      session_id: event.participant.session_id,
      user_id: event.participant.user_id,
      user_name: event.participant.user_name,
      joined_at: new Date(),
      audio: event.participant.audio,
      video: event.participant.video,
      screen: event.participant.screen,
      owner: event.participant.owner,
      local: event.participant.local,
    };

    setCallState(prev => ({
      ...prev,
      participants: [...prev.participants, participant],
    }));
  }, []);

  const handleParticipantLeft = useCallback((event: any) => {
    setCallState(prev => ({
      ...prev,
      participants: prev.participants.filter(p => p.session_id !== event.participant.session_id),
    }));
  }, []);

  const handleParticipantUpdated = useCallback((event: any) => {
    setCallState(prev => ({
      ...prev,
      participants: prev.participants.map(p =>
        p.session_id === event.participant.session_id
          ? { ...p, audio: event.participant.audio, video: event.participant.video, screen: event.participant.screen }
          : p
      ),
    }));
  }, []);

  const handleRecordingStarted = useCallback(() => {
    setCallState(prev => ({
      ...prev,
      isRecording: true,
      recordingStartTime: new Date(),
    }));
  }, []);

  const handleRecordingStopped = useCallback(() => {
    setCallState(prev => ({
      ...prev,
      isRecording: false,
      recordingStartTime: undefined,
    }));
  }, []);

  const handleChatMessage = useCallback((event: any) => {
    const message: ChatMessage = {
      id: Date.now().toString(),
      userId: event.fromId,
      userName: event.data.userName || 'Unknown',
      message: event.data.message,
      timestamp: new Date(),
    };

    setCallState(prev => ({
      ...prev,
      chatMessages: [...prev.chatMessages, message],
    }));
  }, []);

  const handleError = useCallback((event: any) => {
    const error: VideoError = {
      type: 'unknown',
      message: event.error?.msg || 'An error occurred',
      details: event.error,
    };
    
    setCallState(prev => ({ ...prev, error: error.message }));
    onError?.(error.message);
  }, [onError]);

  const handleNetworkQualityChange = useCallback((event: any) => {
    const quality = event.threshold > 0.8 ? 'good' : event.threshold > 0.5 ? 'fair' : 'poor';
    setCallState(prev => ({ ...prev, connectionQuality: quality }));
  }, []);

  // Call controls
  const joinCall = async () => {
    if (!callObject) return;

    try {
      setCallState(prev => ({ ...prev, isConnecting: true }));
      
      await callObject.join({
        url: roomUrl,
        token: token,
        userName: userName,
      });
    } catch (error) {
      handleError({ error: { msg: 'Failed to join call' } });
    }
  };

  const leaveCall = async () => {
    if (!callObject) return;

    try {
      const duration = callState.callDuration;
      await callObject.leave();
      
      // End session on backend
      await videoAPI.endSession(sessionId, Math.floor(duration / 60), token);
    } catch (error) {
      console.error('Error leaving call:', error);
    }
  };

  const toggleAudio = () => {
    if (!callObject) return;
    
    const newAudioState = !callState.hasAudio;
    callObject.setLocalAudio(newAudioState);
    setCallState(prev => ({ ...prev, hasAudio: newAudioState }));
  };

  const toggleVideo = () => {
    if (!callObject) return;
    
    const newVideoState = !callState.hasVideo;
    callObject.setLocalVideo(newVideoState);
    setCallState(prev => ({ ...prev, hasVideo: newVideoState }));
  };

  const toggleScreenShare = async () => {
    if (!callObject) return;

    try {
      if (callState.isScreenSharing) {
        await callObject.stopScreenShare();
      } else {
        await callObject.startScreenShare();
      }
      setCallState(prev => ({ ...prev, isScreenSharing: !prev.isScreenSharing }));
    } catch (error) {
      console.error('Screen share error:', error);
    }
  };

  const startRecording = async () => {
    if (userRole !== 'counselor') return;

    try {
      await videoAPI.startRecording(sessionId, token);
    } catch (error) {
      console.error('Recording start error:', error);
    }
  };

  const stopRecording = async () => {
    if (userRole !== 'counselor') return;

    try {
      await videoAPI.stopRecording(sessionId, token);
    } catch (error) {
      console.error('Recording stop error:', error);
    }
  };

  const sendChatMessage = (message: string) => {
    if (!callObject) return;

    callObject.sendAppMessage({
      message,
      userName,
    });
  };

  const reportIssue = async (type: 'audio' | 'video' | 'connection', description: string) => {
    try {
      await videoAPI.reportConnectionIssue(sessionId, {
        type,
        description,
        reportedBy: userRole === 'counselor' ? 'counselor' : 'user',
      }, token);
    } catch (error) {
      console.error('Issue report error:', error);
    }
  };

  // Connection quality indicator
  const getQualityColor = () => {
    switch (callState.connectionQuality) {
      case 'good': return 'text-green-500';
      case 'fair': return 'text-yellow-500';
      case 'poor': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  if (!callState.isJoined) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-900 text-white">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold mb-2">Ready to join your session?</h1>
          <p className="text-gray-300">
            {userRole === 'counselor' ? 'Your client is waiting' : 'Your counselor is ready'}
          </p>
        </div>

        <div className="mb-8">
          <div className="w-64 h-48 bg-gray-800 rounded-lg flex items-center justify-center mb-4">
            <svg className="w-16 h-16 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </div>
          
          <div className="flex justify-center space-x-4">
            <button
              onClick={toggleAudio}
              className={`p-3 rounded-full ${callState.hasAudio ? 'bg-gray-700' : 'bg-red-600'}`}
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {callState.hasAudio ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                )}
              </svg>
            </button>
            
            <button
              onClick={toggleVideo}
              className={`p-3 rounded-full ${callState.hasVideo ? 'bg-gray-700' : 'bg-red-600'}`}
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {callState.hasVideo ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                )}
              </svg>
            </button>
          </div>
        </div>

        <button
          onClick={joinCall}
          disabled={callState.isConnecting}
          className="bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white px-8 py-3 rounded-lg font-medium transition-colors"
        >
          {callState.isConnecting ? 'Joining...' : 'Join Session'}
        </button>

        {callState.error && (
          <div className="mt-4 p-3 bg-red-600 rounded-lg text-sm">
            {callState.error}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-gray-800">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${getQualityColor()}`}></div>
            <span className="text-sm text-gray-300">
              Connection: {callState.connectionQuality}
            </span>
          </div>
          
          <div className="text-sm text-gray-300">
            Duration: {DailyUtils.formatDuration(callState.callDuration)}
          </div>

          {callState.isRecording && (
            <div className="flex items-center space-x-2 text-red-400">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-sm">Recording</span>
            </div>
          )}
        </div>

        <div className="text-sm text-gray-300">
          {callState.participants.length} participant{callState.participants.length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Video Area */}
      <div className="flex-1 relative" ref={videoRef}>
        {/* This will be populated by Daily.co */}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-center p-6 bg-gray-800 space-x-4">
        <button
          onClick={toggleAudio}
          className={`p-4 rounded-full transition-colors ${
            callState.hasAudio ? 'bg-gray-700 hover:bg-gray-600' : 'bg-red-600 hover:bg-red-700'
          }`}
        >
          <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            {callState.hasAudio ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
            )}
          </svg>
        </button>

        <button
          onClick={toggleVideo}
          className={`p-4 rounded-full transition-colors ${
            callState.hasVideo ? 'bg-gray-700 hover:bg-gray-600' : 'bg-red-600 hover:bg-red-700'
          }`}
        >
          <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            {callState.hasVideo ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
            )}
          </svg>
        </button>

        <button
          onClick={toggleScreenShare}
          className={`p-4 rounded-full transition-colors ${
            callState.isScreenSharing ? 'bg-purple-600 hover:bg-purple-700' : 'bg-gray-700 hover:bg-gray-600'
          }`}
        >
          <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </button>

        {userRole === 'counselor' && (
          <button
            onClick={callState.isRecording ? stopRecording : startRecording}
            className={`p-4 rounded-full transition-colors ${
              callState.isRecording ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-700 hover:bg-gray-600'
            }`}
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {callState.isRecording ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              )}
            </svg>
          </button>
        )}

        <button
          onClick={leaveCall}
          className="p-4 rounded-full bg-red-600 hover:bg-red-700 transition-colors"
        >
          <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 3l1.664 1.664M21 21l-1.5-1.5m-5.485-1.242L12 17l-1.5 1.5m-5.485-1.242L3 21l18-18" />
          </svg>
        </button>
      </div>
    </div>
  );
}
