'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/store/authStore';
import { paymentAPI, PaymentUtils } from '@/lib/payment';
import { PaymentFormProps, PaymentPricing, PaymentError } from '@/types/payment';

export default function PaymentForm({
  sessionId,
  amount,
  currency,
  counselorName,
  sessionDetails,
  onSuccess,
  onError,
  onCancel
}: PaymentFormProps) {
  const { user, tokens } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [pricing, setPricing] = useState<PaymentPricing | null>(null);
  const [selectedMethod, setSelectedMethod] = useState('card');
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const token = tokens?.accessToken;

  useEffect(() => {
    if (token) {
      fetchPricing();
    }
  }, [token]);

  const fetchPricing = async () => {
    try {
      // Extract counselor ID from session (you might need to pass this as a prop)
      const response = await paymentAPI.calculatePricing(
        'counselor-id', // This should be passed as a prop
        sessionDetails.duration,
        currency,
        token!
      );
      setPricing(response.data);
    } catch (err) {
      console.error('Failed to fetch pricing:', err);
    }
  };

  const handlePayment = async () => {
    if (!token || !user) {
      onError({
        type: 'validation',
        message: 'Authentication required',
      });
      return;
    }

    if (!agreedToTerms) {
      setError('Please agree to the terms and conditions');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Initialize payment
      const response = await paymentAPI.initializePayment(sessionId, token);
      
      // Load Paystack script
      await PaymentUtils.loadPaystackScript();

      // Open payment popup or redirect
      if (response.data.authorization_url) {
        // Redirect to Paystack payment page
        window.location.href = response.data.authorization_url;
      } else {
        onError({
          type: 'payment',
          message: 'Failed to initialize payment',
        });
      }
    } catch (err) {
      const error: PaymentError = {
        type: 'payment',
        message: err instanceof Error ? err.message : 'Payment initialization failed',
      };
      onError(error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return PaymentUtils.formatCurrency(amount, currency);
  };

  const paymentMethods = [
    {
      id: 'card',
      name: 'Debit/Credit Card',
      icon: '💳',
      description: 'Pay with your debit or credit card',
    },
    {
      id: 'bank',
      name: 'Bank Transfer',
      icon: '🏦',
      description: 'Transfer from your bank account',
    },
    {
      id: 'ussd',
      name: 'USSD',
      icon: '📱',
      description: 'Pay using USSD code',
    },
  ];

  if (!pricing) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Session Summary */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Session Summary</h2>
        
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Counselor:</span>
            <span className="font-medium text-gray-900">{counselorName}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Date & Time:</span>
            <span className="font-medium text-gray-900">
              {sessionDetails.date} at {sessionDetails.time}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Duration:</span>
            <span className="font-medium text-gray-900">{sessionDetails.duration} minutes</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Session Type:</span>
            <span className="font-medium text-gray-900 capitalize">{sessionDetails.type}</span>
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Method</h3>
        
        <div className="space-y-3">
          {paymentMethods.map((method) => (
            <label
              key={method.id}
              className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedMethod === method.id
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <input
                type="radio"
                name="paymentMethod"
                value={method.id}
                checked={selectedMethod === method.id}
                onChange={(e) => setSelectedMethod(e.target.value)}
                className="sr-only"
              />
              <div className="flex items-center space-x-3 flex-1">
                <span className="text-2xl">{method.icon}</span>
                <div>
                  <p className="font-medium text-gray-900">{method.name}</p>
                  <p className="text-sm text-gray-600">{method.description}</p>
                </div>
              </div>
              {selectedMethod === method.id && (
                <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
            </label>
          ))}
        </div>
      </div>

      {/* Pricing Breakdown */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Summary</h3>
        
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Session Cost:</span>
            <span className="text-gray-900">{formatCurrency(pricing.breakdown.sessionCost)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Platform Fee:</span>
            <span className="text-gray-900">{formatCurrency(pricing.breakdown.platformFee)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Payment Processing:</span>
            <span className="text-gray-900">{formatCurrency(pricing.breakdown.paymentProcessingFee)}</span>
          </div>
          <div className="border-t border-gray-200 pt-3">
            <div className="flex justify-between">
              <span className="text-lg font-semibold text-gray-900">Total:</span>
              <span className="text-lg font-semibold text-gray-900">
                {formatCurrency(pricing.breakdown.total)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Terms and Conditions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <label className="flex items-start space-x-3">
          <input
            type="checkbox"
            checked={agreedToTerms}
            onChange={(e) => setAgreedToTerms(e.target.checked)}
            className="mt-1 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <div className="text-sm text-gray-600">
            <p>
              I agree to the{' '}
              <a href="/terms" className="text-purple-600 hover:text-purple-700 underline">
                Terms of Service
              </a>{' '}
              and{' '}
              <a href="/privacy" className="text-purple-600 hover:text-purple-700 underline">
                Privacy Policy
              </a>
              . I understand that this payment is for a counseling session and is non-refundable except in cases of counselor cancellation.
            </p>
          </div>
        </label>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-4">
        <button
          onClick={onCancel}
          className="flex-1 px-6 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        
        <button
          onClick={handlePayment}
          disabled={loading || !agreedToTerms}
          className="flex-1 px-6 py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Processing...</span>
            </div>
          ) : (
            `Pay ${formatCurrency(pricing.breakdown.total)}`
          )}
        </button>
      </div>

      {/* Security Notice */}
      <div className="mt-6 text-center">
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <span>Secured by Paystack • Your payment information is encrypted</span>
        </div>
      </div>
    </div>
  );
}
