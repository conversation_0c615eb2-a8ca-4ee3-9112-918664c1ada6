{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true, "removeComments": true, "allowSyntheticDefaultImports": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/controllers/*": ["controllers/*"], "@/models/*": ["models/*"], "@/middleware/*": ["middleware/*"], "@/routes/*": ["routes/*"], "@/utils/*": ["utils/*"], "@/config/*": ["config/*"], "@/types/*": ["types/*"]}}, "include": ["src/**/*", "src/types/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}