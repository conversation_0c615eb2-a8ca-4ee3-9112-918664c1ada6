'use client';

import Link from 'next/link';
import { Counselor, COUNSELOR_SPECIALIZATIONS } from '@/types/counselor';

interface CounselorCardProps {
  counselor: Counselor;
}

export default function CounselorCard({ counselor }: CounselorCardProps) {
  const formatRating = (rating: number) => {
    return '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
  };

  const formatPrice = (rate: number, currency: string) => {
    const symbol = currency === 'NGN' ? '₦' : '$';
    return `${symbol}${rate.toLocaleString()}`;
  };

  const getSpecializationInfo = (specialization: string) => {
    return COUNSELOR_SPECIALIZATIONS.find(s => s.value === specialization);
  };

  const formatResponseTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h`;
  };

  return (
    <Link
      href={`/counselors/${counselor._id}`}
      className="group bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 overflow-hidden"
    >
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start space-x-4">
          {/* Profile Picture */}
          <div className="flex-shrink-0">
            {counselor.profile.profilePicture || counselor.user?.profilePicture ? (
              <img
                src={counselor.profile.profilePicture || counselor.user?.profilePicture}
                alt={`${counselor.user?.firstName} ${counselor.user?.lastName}`}
                className="w-16 h-16 rounded-full object-cover"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center">
                <span className="text-xl font-semibold text-purple-600">
                  {counselor.user?.firstName?.charAt(0)}{counselor.user?.lastName?.charAt(0)}
                </span>
              </div>
            )}
          </div>

          {/* Basic Info */}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">
              {counselor.user?.firstName} {counselor.user?.lastName}
            </h3>
            
            {/* Rating and Reviews */}
            {counselor.statistics.totalReviews > 0 && (
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-yellow-400 text-sm">
                  {formatRating(counselor.statistics.averageRating)}
                </span>
                <span className="text-sm text-gray-600">
                  ({counselor.statistics.totalReviews} reviews)
                </span>
              </div>
            )}

            {/* Experience */}
            <p className="text-sm text-gray-600 mt-1">
              {counselor.experience.years} years experience
            </p>

            {/* Languages */}
            <div className="flex flex-wrap gap-1 mt-2">
              {counselor.profile.languages.slice(0, 3).map((language) => (
                <span
                  key={language}
                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                >
                  {language}
                </span>
              ))}
              {counselor.profile.languages.length > 3 && (
                <span className="text-xs text-gray-500">+{counselor.profile.languages.length - 3} more</span>
              )}
            </div>
          </div>

          {/* Status Indicators */}
          <div className="flex flex-col items-end space-y-2">
            {counselor.settings.acceptingNewClients ? (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Available
              </span>
            ) : (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Unavailable
              </span>
            )}

            {counselor.verification.status === 'approved' && (
              <span className="inline-flex items-center text-xs text-blue-600">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Verified
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Bio */}
      <div className="px-6 pb-4">
        <p className="text-sm text-gray-600 line-clamp-3">
          {counselor.bio}
        </p>
      </div>

      {/* Specializations */}
      <div className="px-6 pb-4">
        <div className="flex flex-wrap gap-2">
          {counselor.specializations.slice(0, 3).map((specialization) => {
            const specInfo = getSpecializationInfo(specialization);
            return (
              <span
                key={specialization}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
              >
                {specInfo?.icon} {specInfo?.label || specialization}
              </span>
            );
          })}
          {counselor.specializations.length > 3 && (
            <span className="text-xs text-gray-500">+{counselor.specializations.length - 3} more</span>
          )}
        </div>
      </div>

      {/* Session Types */}
      <div className="px-6 pb-4">
        <div className="flex items-center space-x-4 text-xs text-gray-500">
          <div className="flex items-center">
            <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            {counselor.profile.sessionTypes.join(', ')}
          </div>
          
          <div className="flex items-center">
            <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {counselor.pricing.minimumSessionDuration}min minimum
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="px-6 pb-4">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-lg font-semibold text-gray-900">{counselor.statistics.totalSessions}</p>
            <p className="text-xs text-gray-500">Sessions</p>
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900">{counselor.statistics.completionRate}%</p>
            <p className="text-xs text-gray-500">Completion</p>
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900">{formatResponseTime(counselor.statistics.responseTime)}</p>
            <p className="text-xs text-gray-500">Response</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between">
          {/* Pricing */}
          <div>
            <p className="text-lg font-semibold text-gray-900">
              {formatPrice(counselor.pricing.ratePerMinute, counselor.pricing.currency)}
              <span className="text-sm font-normal text-gray-500">/min</span>
            </p>
            <p className="text-xs text-gray-500">
              From {formatPrice(
                counselor.pricing.ratePerMinute * counselor.pricing.minimumSessionDuration,
                counselor.pricing.currency
              )} per session
            </p>
          </div>

          {/* Action Button */}
          <div className="flex items-center space-x-2">
            {counselor.settings.acceptingNewClients ? (
              <span className="text-purple-600 group-hover:text-purple-700 font-medium text-sm">
                Book Session →
              </span>
            ) : (
              <span className="text-gray-400 text-sm">
                View Profile →
              </span>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}
