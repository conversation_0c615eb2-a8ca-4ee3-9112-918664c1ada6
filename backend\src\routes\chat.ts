import { Router } from "express";
import { Chat<PERSON>ontroller } from "@/controllers/chatController";
import { chatValidations } from "@/utils/validation";
import {
  authenticate,
  optionalAuth,
  requireAdmin,
  requireCounselor,
} from "@/middleware/auth";
import { uploadChatFile, handleUploadError } from "@/middleware/upload";

const router = Router();

// Public routes
router.get("/rooms", optionalAuth, ChatController.getChatRooms);

router.get("/rooms/:roomId", ChatController.getChatRoom);

router.get("/rooms/:roomId/messages", ChatController.getMessages);

router.post(
  "/anonymous-credentials",
  ChatController.generateAnonymousCredentials
);

// Chat room management (admin/moderator only)
router.post(
  "/rooms",
  authenticate,
  requireCounselor,
  chatValidations.createRoom,
  ChatController.createChatRoom
);

// File upload
router.post(
  "/upload",
  authenticate,
  uploadChatFile,
  handleUploadError,
  ChatController.uploadChatFile
);

// Reporting and moderation
router.post("/report", authenticate, ChatController.reportContent);

// Moderator actions
router.put(
  "/messages/:messageId/hide",
  authenticate,
  requireCounselor,
  ChatController.hideMessage
);

router.delete(
  "/messages/:messageId",
  authenticate,
  requireCounselor,
  ChatController.deleteMessage
);

router.put(
  "/rooms/:roomId/users/:userId/mute",
  authenticate,
  requireCounselor,
  ChatController.muteUser
);

router.put(
  "/rooms/:roomId/users/:userId/unmute",
  authenticate,
  requireCounselor,
  ChatController.unmuteUser
);

// Admin routes
router.get(
  "/moderation/queue",
  authenticate,
  requireAdmin,
  ChatController.getModerationQueue
);

router.put(
  "/moderation/reports/:reportId/resolve",
  authenticate,
  requireAdmin,
  ChatController.resolveReport
);

router.get(
  "/moderation/users/:userId/history",
  authenticate,
  requireAdmin,
  ChatController.getUserModerationHistory
);

export default router;
