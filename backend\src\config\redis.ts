import { createClient, RedisClientType } from "redis";
import { logger } from "@/utils/logger";

let redisClient: RedisClientType;

export const connectRedis = async (): Promise<RedisClientType> => {
  try {
    const redisUrl = process.env.REDIS_URL || "redis://localhost:6379";

    redisClient = createClient({
      url: redisUrl,
      password: process.env.REDIS_PASSWORD || undefined,
      socket: {
        reconnectStrategy: (retries: number) => Math.min(retries * 50, 500),
      },
    });

    redisClient.on("error", (err: any) => {
      logger.error("Redis Client Error:", err);
    });

    redisClient.on("connect", () => {
      logger.info("Redis Client Connected");
    });

    redisClient.on("reconnecting", () => {
      logger.info("Redis Client Reconnecting");
    });

    redisClient.on("ready", () => {
      logger.info("Redis Client Ready");
    });

    await redisClient.connect();

    return redisClient;
  } catch (error) {
    logger.error("Error connecting to Redis:", error);
    throw error;
  }
};

export const getRedisClient = (): RedisClientType => {
  if (!redisClient) {
    throw new Error("Redis client not initialized. Call connectRedis() first.");
  }
  return redisClient;
};

export const disconnectRedis = async (): Promise<void> => {
  try {
    if (redisClient) {
      await redisClient.quit();
      logger.info("Redis connection closed");
    }
  } catch (error) {
    logger.error("Error closing Redis connection:", error);
  }
};
