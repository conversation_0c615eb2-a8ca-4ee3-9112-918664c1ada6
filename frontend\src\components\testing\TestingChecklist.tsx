'use client';

import { useState } from 'react';

interface TestItem {
  id: string;
  category: string;
  description: string;
  completed: boolean;
  priority: 'high' | 'medium' | 'low';
}

const testItems: TestItem[] = [
  // Authentication System
  { id: 'auth-1', category: 'Authentication', description: 'Register new user with valid email', completed: false, priority: 'high' },
  { id: 'auth-2', category: 'Authentication', description: 'Login with valid credentials', completed: false, priority: 'high' },
  { id: 'auth-3', category: 'Authentication', description: 'Test invalid login attempts', completed: false, priority: 'high' },
  { id: 'auth-4', category: 'Authentication', description: 'Test guest access functionality', completed: false, priority: 'medium' },
  { id: 'auth-5', category: 'Authentication', description: 'Test logout functionality', completed: false, priority: 'high' },

  // User Dashboard
  { id: 'dash-1', category: 'Dashboard', description: 'View upcoming sessions', completed: false, priority: 'high' },
  { id: 'dash-2', category: 'Dashboard', description: 'Update profile information', completed: false, priority: 'medium' },
  { id: 'dash-3', category: 'Dashboard', description: 'Upload profile picture', completed: false, priority: 'low' },
  { id: 'dash-4', category: 'Dashboard', description: 'Change password', completed: false, priority: 'medium' },

  // Counselor Booking
  { id: 'book-1', category: 'Booking', description: 'Browse counselor list', completed: false, priority: 'high' },
  { id: 'book-2', category: 'Booking', description: 'Filter counselors by specialization', completed: false, priority: 'high' },
  { id: 'book-3', category: 'Booking', description: 'View counselor profile details', completed: false, priority: 'high' },
  { id: 'book-4', category: 'Booking', description: 'Select session date and time', completed: false, priority: 'high' },
  { id: 'book-5', category: 'Booking', description: 'Complete booking process', completed: false, priority: 'high' },

  // Payment System
  { id: 'pay-1', category: 'Payment', description: 'Initialize payment process', completed: false, priority: 'high' },
  { id: 'pay-2', category: 'Payment', description: 'Test different payment methods', completed: false, priority: 'high' },
  { id: 'pay-3', category: 'Payment', description: 'Complete successful payment', completed: false, priority: 'high' },
  { id: 'pay-4', category: 'Payment', description: 'View payment history', completed: false, priority: 'medium' },
  { id: 'pay-5', category: 'Payment', description: 'Request refund', completed: false, priority: 'medium' },

  // Video Sessions
  { id: 'video-1', category: 'Video', description: 'Join video session', completed: false, priority: 'high' },
  { id: 'video-2', category: 'Video', description: 'Test audio mute/unmute', completed: false, priority: 'high' },
  { id: 'video-3', category: 'Video', description: 'Test video on/off', completed: false, priority: 'high' },
  { id: 'video-4', category: 'Video', description: 'Test screen sharing', completed: false, priority: 'medium' },
  { id: 'video-5', category: 'Video', description: 'Test session recording', completed: false, priority: 'medium' },

  // Chatrooms
  { id: 'chat-1', category: 'Chatrooms', description: 'Join public chatroom', completed: false, priority: 'medium' },
  { id: 'chat-2', category: 'Chatrooms', description: 'Send and receive messages', completed: false, priority: 'medium' },
  { id: 'chat-3', category: 'Chatrooms', description: 'Test emoji reactions', completed: false, priority: 'low' },
  { id: 'chat-4', category: 'Chatrooms', description: 'Test typing indicators', completed: false, priority: 'low' },

  // Self-Help Library
  { id: 'lib-1', category: 'Library', description: 'Browse resource categories', completed: false, priority: 'medium' },
  { id: 'lib-2', category: 'Library', description: 'Search for resources', completed: false, priority: 'medium' },
  { id: 'lib-3', category: 'Library', description: 'Read article content', completed: false, priority: 'medium' },
  { id: 'lib-4', category: 'Library', description: 'Rate and review resources', completed: false, priority: 'low' },

  // Counselor Portal
  { id: 'counsel-1', category: 'Counselor Portal', description: 'Complete counselor registration', completed: false, priority: 'high' },
  { id: 'counsel-2', category: 'Counselor Portal', description: 'View counselor dashboard', completed: false, priority: 'high' },
  { id: 'counsel-3', category: 'Counselor Portal', description: 'Manage availability', completed: false, priority: 'high' },
  { id: 'counsel-4', category: 'Counselor Portal', description: 'View earnings and statistics', completed: false, priority: 'medium' },

  // Admin Panel
  { id: 'admin-1', category: 'Admin Panel', description: 'Access admin dashboard', completed: false, priority: 'high' },
  { id: 'admin-2', category: 'Admin Panel', description: 'Manage user accounts', completed: false, priority: 'high' },
  { id: 'admin-3', category: 'Admin Panel', description: 'Review counselor applications', completed: false, priority: 'high' },
  { id: 'admin-4', category: 'Admin Panel', description: 'View platform statistics', completed: false, priority: 'medium' },
];

export default function TestingChecklist() {
  const [items, setItems] = useState<TestItem[]>(testItems);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPriority, setSelectedPriority] = useState<string>('all');

  const categories = ['all', ...Array.from(new Set(items.map(item => item.category)))];
  const priorities = ['all', 'high', 'medium', 'low'];

  const toggleItem = (id: string) => {
    setItems(prev => prev.map(item => 
      item.id === id ? { ...item, completed: !item.completed } : item
    ));
  };

  const filteredItems = items.filter(item => {
    const categoryMatch = selectedCategory === 'all' || item.category === selectedCategory;
    const priorityMatch = selectedPriority === 'all' || item.priority === selectedPriority;
    return categoryMatch && priorityMatch;
  });

  const completedCount = items.filter(item => item.completed).length;
  const totalCount = items.length;
  const completionPercentage = Math.round((completedCount / totalCount) * 100);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoryStats = () => {
    const stats: Record<string, { total: number; completed: number }> = {};
    
    items.forEach(item => {
      if (!stats[item.category]) {
        stats[item.category] = { total: 0, completed: 0 };
      }
      stats[item.category].total++;
      if (item.completed) {
        stats[item.category].completed++;
      }
    });

    return stats;
  };

  const categoryStats = getCategoryStats();

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">🧪 Theramea Testing Checklist</h1>
        <p className="text-gray-600">Track your testing progress across all platform features</p>
      </div>

      {/* Progress Overview */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Overall Progress</h2>
          <span className="text-2xl font-bold text-purple-600">{completionPercentage}%</span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
          <div 
            className="bg-purple-600 h-3 rounded-full transition-all duration-300"
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
        
        <p className="text-sm text-gray-600">
          {completedCount} of {totalCount} tests completed
        </p>
      </div>

      {/* Category Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {Object.entries(categoryStats).map(([category, stats]) => (
          <div key={category} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 className="font-medium text-gray-900 mb-2">{category}</h3>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                {stats.completed}/{stats.total}
              </span>
              <span className="text-sm font-medium text-purple-600">
                {Math.round((stats.completed / stats.total) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className="bg-purple-600 h-2 rounded-full"
                style={{ width: `${(stats.completed / stats.total) * 100}%` }}
              ></div>
            </div>
          </div>
        ))}
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              {priorities.map(priority => (
                <option key={priority} value={priority}>
                  {priority === 'all' ? 'All Priorities' : priority.charAt(0).toUpperCase() + priority.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Test Items */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Test Items</h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredItems.map((item) => (
            <div key={item.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  checked={item.completed}
                  onChange={() => toggleItem(item.id)}
                  className="h-5 w-5 text-purple-600 rounded border-gray-300 focus:ring-purple-500"
                />
                
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-1">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(item.priority)}`}>
                      {item.priority}
                    </span>
                    <span className="text-sm text-gray-500">{item.category}</span>
                  </div>
                  
                  <p className={`text-sm ${item.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                    {item.description}
                  </p>
                </div>
                
                {item.completed && (
                  <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-6 flex flex-wrap gap-4">
        <button
          onClick={() => setItems(prev => prev.map(item => ({ ...item, completed: false })))}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          Reset All
        </button>
        
        <button
          onClick={() => setItems(prev => prev.map(item => 
            item.priority === 'high' ? { ...item, completed: true } : item
          ))}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          Mark High Priority Complete
        </button>
        
        <button
          onClick={() => {
            const data = {
              timestamp: new Date().toISOString(),
              completedTests: items.filter(item => item.completed).length,
              totalTests: items.length,
              completionPercentage,
              categoryStats
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `theramea-testing-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
          }}
          className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
        >
          Export Report
        </button>
      </div>
    </div>
  );
}
