// Video session types and interfaces

export interface VideoSession {
  roomId?: string;
  roomUrl?: string;
  provider: 'daily.co';
  startedAt?: string;
  endedAt?: string;
  actualDuration?: number;
  recordingUrl?: string;
  connectionIssues?: ConnectionIssue[];
}

export interface ConnectionIssue {
  timestamp: string;
  type: 'audio' | 'video' | 'connection';
  description: string;
  reportedBy: 'user' | 'counselor';
}

export interface MeetingToken {
  token: string;
  room_name: string;
  user_name?: string;
  user_id?: string;
  is_owner?: boolean;
  exp?: number;
  enable_screenshare?: boolean;
  start_video_off?: boolean;
  start_audio_off?: boolean;
  enable_recording?: boolean;
}

export interface SessionRecording {
  id: string;
  room_name: string;
  status: 'finished' | 'in-progress' | 'failed';
  max_participants: number;
  duration: number;
  start_ts: number;
  download_link?: string;
  mp4_size?: number;
  mp4_duration?: number;
}

export interface SessionFeedback {
  rating: number;
  comment?: string;
  wouldRecommend: boolean;
  sessionQuality?: number;
  clientEngagement?: number;
  technicalIssues?: boolean;
  notes?: string;
}

export interface VideoCallProps {
  sessionId: string;
  token: string;
  roomUrl: string;
  userRole: 'client' | 'counselor';
  userName: string;
  onCallEnd?: () => void;
  onError?: (error: string) => void;
}

export interface CallState {
  isJoined: boolean;
  isConnecting: boolean;
  participants: Participant[];
  localParticipant?: Participant;
  isRecording: boolean;
  recordingStartTime?: Date;
  connectionQuality: 'good' | 'fair' | 'poor';
  hasAudio: boolean;
  hasVideo: boolean;
  isScreenSharing: boolean;
  chatMessages: ChatMessage[];
  callDuration: number;
  error?: string;
}

export interface Participant {
  session_id: string;
  user_id?: string;
  user_name?: string;
  joined_at: Date;
  audio: boolean;
  video: boolean;
  screen: boolean;
  owner: boolean;
  local: boolean;
}

export interface ChatMessage {
  id: string;
  userId: string;
  userName: string;
  message: string;
  timestamp: Date;
  isSystem?: boolean;
}

export interface MediaDevices {
  cameras: MediaDeviceInfo[];
  microphones: MediaDeviceInfo[];
  speakers: MediaDeviceInfo[];
  selectedCamera?: string;
  selectedMicrophone?: string;
  selectedSpeaker?: string;
}

export interface CallControls {
  toggleAudio: () => void;
  toggleVideo: () => void;
  toggleScreenShare: () => void;
  startRecording: () => void;
  stopRecording: () => void;
  sendChatMessage: (message: string) => void;
  reportIssue: (type: 'audio' | 'video' | 'connection', description: string) => void;
  endCall: () => void;
}

export interface PreJoinSettings {
  userName: string;
  hasAudio: boolean;
  hasVideo: boolean;
  selectedCamera?: string;
  selectedMicrophone?: string;
  selectedSpeaker?: string;
}

// Daily.co event types
export interface DailyEvent {
  action: string;
  participant?: any;
  error?: any;
  recording?: any;
  chat?: any;
}

export interface DailyCallObject {
  join: (options?: any) => Promise<any>;
  leave: () => Promise<any>;
  destroy: () => Promise<any>;
  setLocalAudio: (enabled: boolean) => void;
  setLocalVideo: (enabled: boolean) => void;
  startScreenShare: () => void;
  stopScreenShare: () => void;
  startRecording: () => void;
  stopRecording: () => void;
  sendAppMessage: (message: any, to?: string) => void;
  participants: () => any;
  localParticipant: () => any;
  on: (event: string, handler: (event?: any) => void) => void;
  off: (event: string, handler: (event?: any) => void) => void;
  meetingState: () => string;
  participantCounts: () => { present: number; hidden: number };
  getNetworkStats: () => Promise<any>;
  updateInputSettings: (settings: any) => void;
  updateOutputSettings: (settings: any) => void;
  enumerateDevices: () => Promise<MediaDeviceInfo[]>;
  setCamera: (deviceId: string) => void;
  setMicrophone: (deviceId: string) => void;
  setSpeaker: (deviceId: string) => void;
}

// Session analytics
export interface SessionAnalytics {
  sessionId: string;
  duration: number;
  participantCount: number;
  connectionQuality: {
    average: number;
    min: number;
    max: number;
  };
  audioQuality: {
    average: number;
    dropouts: number;
  };
  videoQuality: {
    average: number;
    freezes: number;
  };
  networkStats: {
    packetsLost: number;
    packetsReceived: number;
    bytesReceived: number;
    bytesSent: number;
  };
  issues: ConnectionIssue[];
  recordingDuration?: number;
  chatMessageCount: number;
}

// Call quality metrics
export interface CallQualityMetrics {
  timestamp: Date;
  audioLevel: number;
  videoResolution: string;
  frameRate: number;
  bitrate: number;
  packetLoss: number;
  jitter: number;
  roundTripTime: number;
}

// Error types
export interface VideoError {
  type: 'connection' | 'permission' | 'device' | 'room' | 'token' | 'recording' | 'unknown';
  message: string;
  code?: string;
  details?: any;
}

// Constants
export const VIDEO_CONSTRAINTS = {
  width: { ideal: 1280, max: 1920 },
  height: { ideal: 720, max: 1080 },
  frameRate: { ideal: 30, max: 60 },
};

export const AUDIO_CONSTRAINTS = {
  echoCancellation: true,
  noiseSuppression: true,
  autoGainControl: true,
  sampleRate: 48000,
};

export const CALL_STATES = {
  IDLE: 'idle',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  RECONNECTING: 'reconnecting',
  DISCONNECTED: 'disconnected',
  ERROR: 'error',
} as const;

export const RECORDING_STATES = {
  IDLE: 'idle',
  STARTING: 'starting',
  RECORDING: 'recording',
  STOPPING: 'stopping',
  STOPPED: 'stopped',
  ERROR: 'error',
} as const;

export const CONNECTION_QUALITY = {
  EXCELLENT: 'excellent',
  GOOD: 'good',
  FAIR: 'fair',
  POOR: 'poor',
  UNKNOWN: 'unknown',
} as const;

export type CallState_Type = typeof CALL_STATES[keyof typeof CALL_STATES];
export type RecordingState = typeof RECORDING_STATES[keyof typeof RECORDING_STATES];
export type ConnectionQuality = typeof CONNECTION_QUALITY[keyof typeof CONNECTION_QUALITY];
