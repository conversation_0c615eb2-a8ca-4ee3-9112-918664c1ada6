import express from "express";
import cors from "cors";
import helmet from "helmet";
import dotenv from "dotenv";
import rateLimit from "express-rate-limit";
import { createServer } from "http";
import { connectDB, disconnectDB } from "@/config/database";
import { connectRedis, disconnectRedis } from "@/config/redis";
import { errorHandler } from "@/middleware/errorHandler";
import { notFound } from "@/middleware/notFound";
import { logger } from "@/utils/logger";
// import { SocketService } from "@/services/socketService";

// Import routes
import authRoutes from "@/routes/auth";
import userRoutes from "@/routes/users";
import counselorRoutes from "@/routes/counselors";
import sessionRoutes from "@/routes/sessions";
import chatRoutes from "@/routes/chat";
import videoRoutes from "@/routes/video";
import adminRoutes from "@/routes/admin";
import resourceRoutes from "@/routes/resources";

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Security middleware
app.use(helmet());

// CORS configuration
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "100"),
  message: "Too many requests from this IP, please try again later.",
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Health check endpoint
app.get("/api/health", (_req, res) => {
  res.status(200).json({
    status: "OK",
    message: "Theramea API is running",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || "development",
  });
});

// API routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/counselors", counselorRoutes);
app.use("/api/sessions", sessionRoutes);
app.use("/api/chat", chatRoutes);
app.use("/api/video", videoRoutes);
app.use("/api/admin", adminRoutes);
app.use("/api/resources", resourceRoutes);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
const startServer = async () => {
  try {
    logger.info("Starting Theramea server...");

    // Try to connect to MongoDB
    try {
      await connectDB();
      logger.info("✓ Connected to MongoDB");
    } catch (mongoError) {
      logger.error("✗ Failed to connect to MongoDB:", mongoError);
      logger.warn(
        "Server will start without MongoDB. Some features may not work."
      );
    }

    // Try to connect to Redis
    try {
      await connectRedis();
      logger.info("✓ Connected to Redis");
    } catch (redisError) {
      logger.error("✗ Failed to connect to Redis:", redisError);
      logger.warn(
        "Server will start without Redis. Real-time features may not work."
      );
    }

    logger.info("Database connection attempts completed.");

    // Create HTTP server
    const server = createServer(app);

    // TODO: Initialize Socket.IO when ready
    // const socketService = new SocketService(server);
    // logger.info('Socket.IO service initialized');

    // Start HTTP server
    server.listen(PORT, () => {
      logger.info(
        `Server running on port ${PORT} in ${process.env.NODE_ENV} mode`
      );
      logger.info(`Health check available at: http://localhost:${PORT}/health`);
    });

    // Graceful shutdown
    const gracefulShutdown = async () => {
      logger.info("Shutting down gracefully...");

      try {
        // Close HTTP server
        server.close(async () => {
          logger.info("HTTP server closed");

          // Close database connections
          await disconnectDB();
          await disconnectRedis();

          logger.info("All connections closed. Process terminated");
          process.exit(0);
        });
      } catch (error) {
        logger.error("Error during graceful shutdown:", error);
        process.exit(1);
      }
    };

    process.on("SIGTERM", gracefulShutdown);
    process.on("SIGINT", gracefulShutdown);

    process.on("SIGINT", () => {
      logger.info("SIGINT received, shutting down gracefully");
      server.close(() => {
        logger.info("Process terminated");
        process.exit(0);
      });
    });
  } catch (error) {
    logger.error("Failed to start server:", error);
    process.exit(1);
  }
};

startServer();

export default app;
