import { Request, Response, NextFunction } from "express";
import { JWTService, JWTPayload } from "@/utils/jwt";
import { User, IUser } from "@/models/User";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";

// Extend Express Request interface to include user properties
declare module "express-serve-static-core" {
  interface Request {
    user?: IUser;
    userId?: string;
    userRole?: string;
  }
}

/**
 * Authentication middleware - verifies JWT token
 */
export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = JWTService.extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      return next(createError("Access token is required", 401));
    }

    // Verify token
    const payload: JWTPayload = JWTService.verifyAccessToken(token);

    // Get user from database
    const user = (await User.findById(payload.userId).select(
      "+password"
    )) as IUser | null;

    if (!user) {
      return next(createError("User not found", 401));
    }

    if (!user.isActive) {
      return next(createError("Account is deactivated", 401));
    }

    // Attach user to request
    req.user = user;
    req.userId = user._id.toString();
    req.userRole = user.role;

    next();
  } catch (error) {
    logger.error("Authentication error:", error);
    return next(createError("Invalid or expired token", 401));
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = JWTService.extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      return next(); // Continue without authentication
    }

    const payload: JWTPayload = JWTService.verifyAccessToken(token);
    const user = (await User.findById(payload.userId)) as IUser | null;

    if (user && user.isActive) {
      req.user = user;
      req.userId = user._id.toString();
      req.userRole = user.role;
    }

    next();
  } catch (error) {
    // Log error but don't fail the request
    logger.warn("Optional authentication failed:", error);
    next();
  }
};

/**
 * Role-based authorization middleware
 */
export const authorize = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createError("Authentication required", 401));
    }

    if (!roles.includes((req.user as IUser).role)) {
      return next(createError("Insufficient permissions", 403));
    }

    next();
  };
};

/**
 * Email verification middleware
 */
export const requireEmailVerification = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!req.user) {
    return next(createError("Authentication required", 401));
  }

  if (!(req.user as IUser).isEmailVerified) {
    return next(createError("Email verification required", 403));
  }

  next();
};

/**
 * Admin authorization middleware
 */
export const requireAdmin = authorize("admin", "superadmin");

/**
 * Super admin authorization middleware
 */
export const requireSuperAdmin = authorize("superadmin");

/**
 * Counselor authorization middleware
 */
export const requireCounselor = authorize("counselor", "admin", "superadmin");

/**
 * User or higher authorization middleware
 */
export const requireUser = authorize(
  "user",
  "counselor",
  "admin",
  "superadmin"
);

/**
 * Rate limiting for authentication endpoints
 */
export const authRateLimit = (
  maxAttempts: number = 5,
  windowMs: number = 15 * 60 * 1000
) => {
  const attempts = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction) => {
    const key = req.ip || "unknown";
    const now = Date.now();

    const userAttempts = attempts.get(key);

    if (userAttempts) {
      if (now > userAttempts.resetTime) {
        // Reset window
        attempts.set(key, { count: 1, resetTime: now + windowMs });
      } else if (userAttempts.count >= maxAttempts) {
        return next(
          createError(
            "Too many authentication attempts. Please try again later.",
            429
          )
        );
      } else {
        userAttempts.count++;
      }
    } else {
      attempts.set(key, { count: 1, resetTime: now + windowMs });
    }

    next();
  };
};

/**
 * Middleware to check if user owns resource
 */
export const requireOwnership = (resourceIdParam: string = "id") => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createError("Authentication required", 401));
    }

    const resourceId = req.params[resourceIdParam];
    const userId = (req.user as IUser)._id.toString();

    // Super admins can access any resource
    if ((req.user as IUser).role === "superadmin") {
      return next();
    }

    // Admins can access most resources
    if ((req.user as IUser).role === "admin") {
      return next();
    }

    // Check if user owns the resource
    if (resourceId !== userId) {
      return next(
        createError(
          "Access denied. You can only access your own resources.",
          403
        )
      );
    }

    next();
  };
};

/**
 * Middleware to validate session ownership
 */
export const requireSessionAccess = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return next(createError("Authentication required", 401));
    }

    const sessionId = req.params.sessionId || req.params.id;

    if (!sessionId) {
      return next(createError("Session ID is required", 400));
    }

    // Import Session model here to avoid circular dependency
    const { Session } = await import("@/models/Session");
    const session = await Session.findById(sessionId);

    if (!session) {
      return next(createError("Session not found", 404));
    }

    const userId = (req.user as IUser)._id.toString();
    const isUser = session.userId.toString() === userId;
    const isCounselor = session.counselorId.toString() === userId;
    const isAdmin = ["admin", "superadmin"].includes((req.user as IUser).role);

    if (!isUser && !isCounselor && !isAdmin) {
      return next(
        createError(
          "Access denied. You are not authorized to access this session.",
          403
        )
      );
    }

    // Attach session to request for use in controller
    (req as any).session = session;
    next();
  } catch (error) {
    logger.error("Session access validation error:", error);
    next(createError("Error validating session access", 500));
  }
};

/**
 * Middleware to track login attempts
 */
export const trackLoginAttempt = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const originalSend = res.send;

  res.send = function (data) {
    // Track successful login
    if (res.statusCode === 200 && req.body.email) {
      User.findOneAndUpdate(
        { email: req.body.email },
        {
          lastLogin: new Date(),
          $push: {
            loginHistory: {
              timestamp: new Date(),
              ipAddress: req.ip || "unknown",
              userAgent: req.get("User-Agent") || "unknown",
              location: req.get("CF-IPCountry") || "unknown",
            },
          },
        },
        { new: true }
      ).catch((error) => {
        logger.error("Error tracking login attempt:", error);
      });
    }

    return originalSend.call(this, data);
  };

  next();
};
