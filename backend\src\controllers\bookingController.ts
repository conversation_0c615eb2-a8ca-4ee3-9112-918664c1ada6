import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { BookingService } from '@/services/bookingService';
import { PaymentService } from '@/services/paymentService';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

export class BookingController {
  /**
   * Create a new booking
   */
  static async createBooking(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400));
      }

      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const bookingData = {
        ...req.body,
        clientId: req.user._id.toString()
      };

      const session = await BookingService.createBooking(bookingData);

      res.status(201).json({
        success: true,
        message: 'Booking created successfully',
        data: { session }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get bookings list
   */
  static async getBookings(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const {
        page = 1,
        limit = 20,
        sortBy = 'scheduledAt',
        sortOrder = 'desc',
        status,
        sessionType,
        dateFrom,
        dateTo
      } = req.query;

      const filters: any = {};

      // Filter by user role
      if (req.user.role === 'client') {
        filters.clientId = req.user._id.toString();
      } else if (req.user.role === 'counselor') {
        // Get counselor ID from user
        const { CounselorService } = await import('@/services/counselorService');
        const counselor = await CounselorService.getCounselorByUserId(req.user._id.toString());
        filters.counselorId = counselor._id.toString();
      }

      if (status) {
        filters.status = Array.isArray(status) ? status : [status];
      }
      if (sessionType) filters.sessionType = sessionType;
      if (dateFrom) filters.dateFrom = new Date(dateFrom as string);
      if (dateTo) filters.dateTo = new Date(dateTo as string);

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc'
      };

      const result = await BookingService.getBookings(filters, options);

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get specific booking
   */
  static async getBooking(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      const session = await BookingService.getBooking(sessionId);

      // Check authorization
      const isClient = session.clientId.toString() === req.user._id.toString();
      const isCounselor = session.counselorId.toString() === req.user._id.toString();
      const isAdmin = req.user.role === 'admin';

      if (!isClient && !isCounselor && !isAdmin) {
        return next(createError('Unauthorized to view this booking', 403));
      }

      res.json({
        success: true,
        data: { session }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Initialize payment for booking
   */
  static async initializePayment(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      
      // Verify user owns this booking
      const session = await BookingService.getBooking(sessionId);
      if (session.clientId.toString() !== req.user._id.toString()) {
        return next(createError('Unauthorized to pay for this booking', 403));
      }

      const paymentData = await BookingService.initializePayment(sessionId);

      res.json({
        success: true,
        message: 'Payment initialized successfully',
        data: paymentData
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Verify payment
   */
  static async verifyPayment(req: Request, res: Response, next: NextFunction) {
    try {
      const { reference } = req.params;
      const session = await BookingService.verifyPayment(reference);

      res.json({
        success: true,
        message: 'Payment verified successfully',
        data: { session }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Cancel booking
   */
  static async cancelBooking(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      const { reason } = req.body;

      if (!reason) {
        return next(createError('Cancellation reason is required', 400));
      }

      // Verify user can cancel this booking
      const session = await BookingService.getBooking(sessionId);
      const isClient = session.clientId.toString() === req.user._id.toString();
      const isCounselor = session.counselorId.toString() === req.user._id.toString();

      if (!isClient && !isCounselor) {
        return next(createError('Unauthorized to cancel this booking', 403));
      }

      const cancelledSession = await BookingService.cancelBooking(
        sessionId,
        req.user._id.toString(),
        reason
      );

      res.json({
        success: true,
        message: 'Booking cancelled successfully',
        data: { session: cancelledSession }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reschedule booking
   */
  static async rescheduleBooking(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      const { sessionId } = req.params;
      const { newScheduledAt, reason } = req.body;

      if (!newScheduledAt) {
        return next(createError('New scheduled time is required', 400));
      }

      // Verify user can reschedule this booking
      const session = await BookingService.getBooking(sessionId);
      const isClient = session.clientId.toString() === req.user._id.toString();
      const isCounselor = session.counselorId.toString() === req.user._id.toString();

      if (!isClient && !isCounselor) {
        return next(createError('Unauthorized to reschedule this booking', 403));
      }

      const rescheduledSession = await BookingService.rescheduleBooking(
        sessionId,
        new Date(newScheduledAt),
        req.user._id.toString(),
        reason
      );

      res.json({
        success: true,
        message: 'Booking rescheduled successfully',
        data: { session: rescheduledSession }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Approve booking (counselor only)
   */
  static async approveBooking(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      if (req.user.role !== 'counselor') {
        return next(createError('Only counselors can approve bookings', 403));
      }

      const { sessionId } = req.params;
      
      // Get counselor ID
      const { CounselorService } = await import('@/services/counselorService');
      const counselor = await CounselorService.getCounselorByUserId(req.user._id.toString());

      const approvedSession = await BookingService.approveBooking(
        sessionId,
        counselor._id.toString()
      );

      res.json({
        success: true,
        message: 'Booking approved successfully',
        data: { session: approvedSession }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reject booking (counselor only)
   */
  static async rejectBooking(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      if (req.user.role !== 'counselor') {
        return next(createError('Only counselors can reject bookings', 403));
      }

      const { sessionId } = req.params;
      const { reason } = req.body;

      if (!reason) {
        return next(createError('Rejection reason is required', 400));
      }

      // Get counselor ID
      const { CounselorService } = await import('@/services/counselorService');
      const counselor = await CounselorService.getCounselorByUserId(req.user._id.toString());

      const rejectedSession = await BookingService.rejectBooking(
        sessionId,
        counselor._id.toString(),
        reason
      );

      res.json({
        success: true,
        message: 'Booking rejected successfully',
        data: { session: rejectedSession }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Calculate pricing for a session
   */
  static async calculatePricing(req: Request, res: Response, next: NextFunction) {
    try {
      const { counselorId, duration, isUrgent } = req.query;

      if (!counselorId || !duration) {
        return next(createError('Counselor ID and duration are required', 400));
      }

      const pricing = await BookingService.calculatePricing(
        counselorId as string,
        parseInt(duration as string),
        isUrgent === 'true'
      );

      res.json({
        success: true,
        data: { pricing }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Handle payment webhook
   */
  static async handlePaymentWebhook(req: Request, res: Response, next: NextFunction) {
    try {
      const signature = req.headers['x-paystack-signature'] as string;
      const payload = JSON.stringify(req.body);

      // Validate webhook signature
      if (!PaymentService.validateWebhookSignature(payload, signature)) {
        return next(createError('Invalid webhook signature', 400));
      }

      const event = req.body;

      switch (event.event) {
        case 'charge.success':
          await BookingService.verifyPayment(event.data.reference);
          logger.info(`Payment webhook processed: ${event.data.reference}`);
          break;

        case 'charge.failed':
          // Handle failed payment
          logger.info(`Payment failed webhook: ${event.data.reference}`);
          break;

        default:
          logger.info(`Unhandled webhook event: ${event.event}`);
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Payment webhook error:', error);
      res.status(200).json({ success: false }); // Always return 200 to Paystack
    }
  }

  /**
   * Get supported banks
   */
  static async getSupportedBanks(req: Request, res: Response, next: NextFunction) {
    try {
      const { country = 'nigeria' } = req.query;
      const banks = await PaymentService.getSupportedBanks(country as string);

      res.json({
        success: true,
        data: { banks }
      });
    } catch (error) {
      next(error);
    }
  }
}
