import mongoose, { Document, Schema } from 'mongoose';

export interface ICounselor extends Document {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  bio: string;
  specializations: string[];
  qualifications: {
    degree: string;
    institution: string;
    year: number;
    certificateUrl?: string;
  }[];
  licenses: {
    type: string;
    number: string;
    issuingAuthority: string;
    expiryDate: Date;
    documentUrl?: string;
  }[];
  experience: {
    years: number;
    description: string;
  };
  pricing: {
    currency: 'NGN' | 'USD';
    ratePerMinute: number;
    minimumSessionDuration: number; // in minutes
  };
  availability: {
    timezone: string;
    schedule: {
      [key: string]: { // day of week (monday, tuesday, etc.)
        isAvailable: boolean;
        timeSlots: {
          startTime: string; // HH:mm format
          endTime: string;   // HH:mm format
        }[];
      };
    };
    unavailableDates: Date[];
    availabilityExpiresAt: Date; // When current availability expires
  };
  verification: {
    status: 'pending' | 'approved' | 'rejected' | 'suspended';
    submittedAt: Date;
    reviewedAt?: Date;
    reviewedBy?: mongoose.Types.ObjectId;
    rejectionReason?: string;
    documents: {
      idDocument: {
        url: string;
        verified: boolean;
      };
      certificates: {
        url: string;
        verified: boolean;
        type: string;
      }[];
    };
  };
  profile: {
    profilePicture?: string;
    languages: string[];
    approachDescription: string;
    sessionTypes: ('individual' | 'group' | 'couples' | 'family')[];
  };
  statistics: {
    totalSessions: number;
    totalEarnings: number;
    averageRating: number;
    totalReviews: number;
    completionRate: number; // percentage of sessions completed
    responseTime: number; // average response time in minutes
  };
  settings: {
    acceptingNewClients: boolean;
    autoAcceptBookings: boolean;
    requiresApproval: boolean;
    cancellationPolicy: string;
    reschedulePolicy: string;
  };
  bankDetails: {
    accountName: string;
    accountNumber: string;
    bankName: string;
    bankCode: string;
    currency: 'NGN' | 'USD';
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const counselorSchema = new Schema<ICounselor>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  bio: {
    type: String,
    required: [true, 'Bio is required'],
    maxlength: [1000, 'Bio cannot exceed 1000 characters']
  },
  specializations: [{
    type: String,
    enum: [
      'anxiety-disorders',
      'depression',
      'trauma-ptsd',
      'relationship-counseling',
      'family-therapy',
      'addiction-recovery',
      'grief-counseling',
      'stress-management',
      'career-counseling',
      'adolescent-therapy',
      'couples-therapy',
      'eating-disorders',
      'anger-management',
      'sleep-disorders',
      'life-coaching',
      'spiritual-counseling'
    ],
    required: true
  }],
  qualifications: [{
    degree: {
      type: String,
      required: true
    },
    institution: {
      type: String,
      required: true
    },
    year: {
      type: Number,
      required: true,
      min: 1950,
      max: new Date().getFullYear()
    },
    certificateUrl: String
  }],
  licenses: [{
    type: {
      type: String,
      required: true
    },
    number: {
      type: String,
      required: true
    },
    issuingAuthority: {
      type: String,
      required: true
    },
    expiryDate: {
      type: Date,
      required: true
    },
    documentUrl: String
  }],
  experience: {
    years: {
      type: Number,
      required: true,
      min: 0,
      max: 50
    },
    description: {
      type: String,
      required: true,
      maxlength: [500, 'Experience description cannot exceed 500 characters']
    }
  },
  pricing: {
    currency: {
      type: String,
      enum: ['NGN', 'USD'],
      required: true,
      default: 'NGN'
    },
    ratePerMinute: {
      type: Number,
      required: true,
      min: [1, 'Rate per minute must be at least 1']
    },
    minimumSessionDuration: {
      type: Number,
      required: true,
      default: 15,
      min: [15, 'Minimum session duration must be at least 15 minutes']
    }
  },
  availability: {
    timezone: {
      type: String,
      required: true,
      default: 'Africa/Lagos'
    },
    schedule: {
      type: Map,
      of: {
        isAvailable: { type: Boolean, default: false },
        timeSlots: [{
          startTime: {
            type: String,
            match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
          },
          endTime: {
            type: String,
            match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
          }
        }]
      },
      default: {}
    },
    unavailableDates: [Date],
    availabilityExpiresAt: {
      type: Date,
      required: true,
      default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    }
  },
  verification: {
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'suspended'],
      default: 'pending'
    },
    submittedAt: {
      type: Date,
      default: Date.now
    },
    reviewedAt: Date,
    reviewedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    rejectionReason: String,
    documents: {
      idDocument: {
        url: { type: String, required: true },
        verified: { type: Boolean, default: false }
      },
      certificates: [{
        url: { type: String, required: true },
        verified: { type: Boolean, default: false },
        type: String
      }]
    }
  },
  profile: {
    profilePicture: String,
    languages: [{
      type: String,
      default: ['English']
    }],
    approachDescription: {
      type: String,
      maxlength: [500, 'Approach description cannot exceed 500 characters']
    },
    sessionTypes: [{
      type: String,
      enum: ['individual', 'group', 'couples', 'family'],
      default: ['individual']
    }]
  },
  statistics: {
    totalSessions: { type: Number, default: 0 },
    totalEarnings: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0, min: 0, max: 5 },
    totalReviews: { type: Number, default: 0 },
    completionRate: { type: Number, default: 0, min: 0, max: 100 },
    responseTime: { type: Number, default: 0 }
  },
  settings: {
    acceptingNewClients: { type: Boolean, default: true },
    autoAcceptBookings: { type: Boolean, default: false },
    requiresApproval: { type: Boolean, default: true },
    cancellationPolicy: {
      type: String,
      default: 'Cancellations must be made at least 12 hours in advance for a full refund.'
    },
    reschedulePolicy: {
      type: String,
      default: 'Sessions can be rescheduled up to 12 hours before the scheduled time.'
    }
  },
  bankDetails: {
    accountName: String,
    accountNumber: String,
    bankName: String,
    bankCode: String,
    currency: {
      type: String,
      enum: ['NGN', 'USD'],
      default: 'NGN'
    }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
counselorSchema.index({ userId: 1 });
counselorSchema.index({ 'verification.status': 1 });
counselorSchema.index({ specializations: 1 });
counselorSchema.index({ 'pricing.ratePerMinute': 1 });
counselorSchema.index({ 'statistics.averageRating': -1 });
counselorSchema.index({ isActive: 1 });
counselorSchema.index({ 'availability.availabilityExpiresAt': 1 });

// Virtual to populate user details
counselorSchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

export const Counselor = mongoose.model<ICounselor>('Counselor', counselorSchema);
