// Authentication types

export interface User {
  _id: string;
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  isEmailVerified: boolean;
  areasOfInterest: string[];
  location?: {
    country?: string;
    city?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  profilePicture?: string;
  role: 'user' | 'counselor' | 'admin' | 'superadmin';
  isActive: boolean;
  lastLogin?: string;
  preferences: {
    notifications: {
      email: boolean;
      push: boolean;
      sessionReminders: boolean;
      chatMessages: boolean;
    };
    privacy: {
      showOnlineStatus: boolean;
      allowDirectMessages: boolean;
    };
  };
  socialAuth?: {
    google?: {
      id: string;
      email: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isGuest: boolean;
  guestToken: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface LoginFormData {
  email: string;
  password: string;
}

export interface RegisterFormData {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  areasOfInterest: string[];
}

export interface ForgotPasswordFormData {
  email: string;
}

export interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

export interface ChangePasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Areas of interest options
export const AREAS_OF_INTEREST = [
  { value: 'relationships', label: 'Relationships' },
  { value: 'school', label: 'School & Education' },
  { value: 'anxiety', label: 'Anxiety' },
  { value: 'addiction', label: 'Addiction' },
  { value: 'stress', label: 'Stress Management' },
  { value: 'mental-health', label: 'Mental Health' },
  { value: 'depression', label: 'Depression' },
  { value: 'family', label: 'Family Issues' },
  { value: 'career', label: 'Career & Work' },
  { value: 'self-discovery', label: 'Self-Discovery' },
  { value: 'grief', label: 'Grief & Loss' },
  { value: 'trauma', label: 'Trauma & PTSD' },
  { value: 'anger-management', label: 'Anger Management' },
  { value: 'eating-disorders', label: 'Eating Disorders' },
  { value: 'sleep-issues', label: 'Sleep Issues' },
] as const;

export type AreaOfInterest = typeof AREAS_OF_INTEREST[number]['value'];
