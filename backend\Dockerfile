# Use Node.js 18 LTS as base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies including curl for health check and netcat for waiting
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    curl \
    netcat-openbsd \
    && ln -sf python3 /usr/bin/python

# Copy package files
COPY package*.json ./

# Install dependencies (use npm install for development)
RUN npm install

# Copy source code
COPY . .

# Make start script executable and create uploads directory
RUN chmod +x start.sh && \
    mkdir -p uploads && \
    chown -R node:node /app

# Switch to node user
USER node

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:5000/api/health || exit 1

# Start the application
CMD ["./start.sh"]
