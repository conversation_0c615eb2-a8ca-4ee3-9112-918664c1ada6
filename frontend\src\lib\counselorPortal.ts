// Counselor Portal API functions
import { Counselor, Session } from '@/types/counselor';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

class CounselorPortalAPI {
  private getHeaders(token?: string) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }

  // Profile Management
  async getMyProfile(token: string): Promise<{
    success: boolean;
    data: { counselor: Counselor };
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/me/profile`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch profile');
    }

    return response.json();
  }

  async updateProfile(profileData: Partial<Counselor>, token: string): Promise<{
    success: boolean;
    message: string;
    data: { counselor: Counselor };
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/me/profile`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify(profileData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update profile');
    }

    return response.json();
  }

  // Availability Management
  async updateAvailability(availabilityData: {
    schedule: {
      [key: string]: {
        isAvailable: boolean;
        timeSlots: {
          startTime: string;
          endTime: string;
        }[];
      };
    };
    unavailableDates?: string[];
    timezone: string;
  }, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/me/availability`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify(availabilityData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update availability');
    }

    return response.json();
  }

  async blockTimeSlots(slots: {
    date: string;
    startTime: string;
    endTime: string;
    reason?: string;
  }[], token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/me/block-slots`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify({ slots }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to block time slots');
    }

    return response.json();
  }

  async unblockTimeSlots(slots: {
    date: string;
    startTime: string;
    endTime: string;
  }[], token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/me/unblock-slots`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify({ slots }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to unblock time slots');
    }

    return response.json();
  }

  // Session Management
  async getMySessions(status?: string, page: number = 1, limit: number = 20, token?: string): Promise<{
    success: boolean;
    data: {
      sessions: Session[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      role: 'counselor',
    });
    
    if (status) queryParams.append('status', status);

    const response = await fetch(`${API_BASE_URL}/sessions?${queryParams}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch sessions');
    }

    return response.json();
  }

  async updateSessionStatus(sessionId: string, status: string, notes?: string, token?: string): Promise<{
    success: boolean;
    message: string;
    data: { session: Session };
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/status`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify({ status, notes }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update session status');
    }

    return response.json();
  }

  async addSessionNotes(sessionId: string, notes: {
    counselorNotes?: string;
    sessionSummary?: string;
    nextSteps?: string;
  }, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/notes`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify(notes),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to add session notes');
    }

    return response.json();
  }

  // Statistics and Analytics
  async getStats(token: string): Promise<{
    success: boolean;
    data: {
      totalSessions: number;
      totalEarnings: number;
      averageRating: number;
      totalReviews: number;
      completionRate: number;
      responseTime: number;
      upcomingSessions: number;
      monthlyEarnings: number;
      weeklyStats: {
        week: string;
        sessions: number;
        earnings: number;
      }[];
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/me/stats`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch statistics');
    }

    return response.json();
  }

  async getDetailedStats(period: 'week' | 'month' | 'year' = 'month', token: string): Promise<{
    success: boolean;
    data: {
      earnings: {
        total: number;
        byPeriod: { period: string; amount: number }[];
        currency: string;
      };
      sessions: {
        total: number;
        completed: number;
        cancelled: number;
        noShow: number;
        byStatus: { status: string; count: number }[];
      };
      ratings: {
        average: number;
        distribution: { rating: number; count: number }[];
        recent: {
          rating: number;
          comment?: string;
          date: string;
        }[];
      };
      clients: {
        total: number;
        returning: number;
        new: number;
      };
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/me/detailed-stats?period=${period}`, {
      method: 'GET',
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch detailed statistics');
    }

    return response.json();
  }

  // Settings Management
  async updateSettings(settings: {
    acceptingNewClients?: boolean;
    autoAcceptBookings?: boolean;
    requiresApproval?: boolean;
    cancellationPolicy?: string;
    reschedulePolicy?: string;
  }, token: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/me/settings`, {
      method: 'PUT',
      headers: this.getHeaders(token),
      body: JSON.stringify(settings),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update settings');
    }

    return response.json();
  }

  // Document Upload
  async uploadVerificationDocuments(formData: FormData, token: string): Promise<{
    success: boolean;
    message: string;
    data: {
      uploadedFiles: {
        fieldname: string;
        filename: string;
        url: string;
      }[];
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/me/verification-documents`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to upload documents');
    }

    return response.json();
  }

  // Registration
  async registerAsCounselor(registrationData: {
    bio: string;
    specializations: string[];
    qualifications: {
      degree: string;
      institution: string;
      year: number;
    }[];
    licenses: {
      type: string;
      number: string;
      issuingAuthority: string;
      expiryDate: string;
    }[];
    experience: {
      years: number;
      description: string;
    };
    pricing: {
      currency: 'NGN' | 'USD';
      ratePerMinute: number;
      minimumSessionDuration: number;
    };
    profile: {
      languages: string[];
      approachDescription: string;
      sessionTypes: string[];
    };
    bankDetails: {
      accountName: string;
      accountNumber: string;
      bankName: string;
      bankCode: string;
      currency: 'NGN' | 'USD';
    };
  }, token: string): Promise<{
    success: boolean;
    message: string;
    data: { counselor: Counselor };
  }> {
    const response = await fetch(`${API_BASE_URL}/counselors/register`, {
      method: 'POST',
      headers: this.getHeaders(token),
      body: JSON.stringify(registrationData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to register as counselor');
    }

    return response.json();
  }
}

export const counselorPortalAPI = new CounselorPortalAPI();
