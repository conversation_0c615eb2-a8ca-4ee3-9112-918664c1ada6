import { Session, ISession } from "@/models/Session";
import { VideoService } from "@/services/videoService";
import { CounselorStatsService } from "@/services/counselorStatsService";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";
import { sendEmail } from "@/utils/email";

export interface SessionUpdate {
  status?: "scheduled" | "in-progress" | "completed" | "cancelled" | "no-show";
  startedAt?: Date;
  endedAt?: Date;
  actualDuration?: number;
  notes?: {
    counselorNotes?: string;
    clientNotes?: string;
    sessionSummary?: string;
  };
  outcome?: {
    rating?: number;
    feedback?: string;
    followUpRequired?: boolean;
    nextSessionRecommended?: boolean;
  };
}

export interface SessionFeedback {
  rating: number; // 1-5 stars
  feedback: string;
  categories: {
    communication: number;
    helpfulness: number;
    professionalism: number;
    overallExperience: number;
  };
  wouldRecommend: boolean;
  anonymous?: boolean;
}

export interface SessionStats {
  totalSessions: number;
  completedSessions: number;
  cancelledSessions: number;
  noShowSessions: number;
  averageRating: number;
  averageDuration: number;
  completionRate: number;
}

export class SessionService {
  /**
   * Start a session
   */
  static async startSession(
    sessionId: string,
    startedBy: string
  ): Promise<ISession> {
    try {
      const session = await Session.findById(sessionId)
        .populate("userId")
        .populate("counselorId");

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (session.status !== "scheduled") {
        throw createError("Session is not scheduled", 400);
      }

      // Check if session time is appropriate (within 15 minutes of scheduled time)
      const now = new Date();
      const scheduledTime = new Date(session.scheduledAt);
      const timeDiff =
        Math.abs(now.getTime() - scheduledTime.getTime()) / (1000 * 60); // minutes

      if (timeDiff > 15) {
        throw createError(
          "Session can only be started within 15 minutes of scheduled time",
          400
        );
      }

      // Create video room if not exists
      if (!session.videoSession?.roomId) {
        await VideoService.createSessionRoom(sessionId);
        // Refresh session data
        const updatedSession = await Session.findById(sessionId);
        if (updatedSession) {
          Object.assign(session, updatedSession);
        }
      }

      // Update session status
      session.status = "in-progress";
      session.startedAt = now;

      await session.save();

      // Send session started notifications
      await this.sendSessionStartedNotifications(session);

      logger.info(`Session started: ${sessionId} by ${startedBy}`);
      return session;
    } catch (error) {
      logger.error("Start session error:", error);
      throw error;
    }
  }

  /**
   * End a session
   */
  static async endSession(
    sessionId: string,
    endedBy: string,
    sessionData: SessionUpdate
  ): Promise<ISession> {
    try {
      const session = await Session.findById(sessionId)
        .populate("userId")
        .populate("counselorId");

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (session.status !== "in-progress") {
        throw createError("Session is not in progress", 400);
      }

      const endTime = new Date();
      const actualDuration = session.startedAt
        ? Math.round(
            (endTime.getTime() - session.startedAt.getTime()) / (1000 * 60)
          )
        : session.duration;

      // Update session
      session.status = "completed";
      session.endedAt = endTime;
      session.actualDuration = actualDuration;

      if (sessionData.notes) {
        session.notes = { ...session.notes, ...sessionData.notes };
      }

      if (sessionData.outcome) {
        session.outcome = sessionData.outcome;
      }

      await session.save();

      // Complete video session
      if (session.videoSession?.roomId) {
        await VideoService.completeVideoSession(sessionId);
      }

      // Update counselor statistics
      await CounselorStatsService.updateSessionStats(
        session.counselorId.toString(),
        {
          earnings: session.pricing.totalAmount - session.pricing.platformFee,
          rating: sessionData.outcome?.rating,
          completed: true,
        }
      );

      // Send session completed notifications
      await this.sendSessionCompletedNotifications(session);

      logger.info(`Session completed: ${sessionId} by ${endedBy}`);
      return session;
    } catch (error) {
      logger.error("End session error:", error);
      throw error;
    }
  }

  /**
   * Mark session as no-show
   */
  static async markNoShow(
    sessionId: string,
    markedBy: string,
    reason: string
  ): Promise<ISession> {
    try {
      const session = await Session.findById(sessionId)
        .populate("userId")
        .populate("counselorId");

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (session.status !== "scheduled" && session.status !== "in-progress") {
        throw createError("Session cannot be marked as no-show", 400);
      }

      // Update session
      session.status = "no-show";
      session.noShow = {
        markedBy: markedBy as any,
        markedAt: new Date(),
        reason,
      };

      await session.save();

      // Handle no-show policy (e.g., charge client, compensate counselor)
      await this.handleNoShowPolicy(session);

      // Send no-show notifications
      await this.sendNoShowNotifications(session, reason);

      logger.info(`Session marked as no-show: ${sessionId} by ${markedBy}`);
      return session;
    } catch (error) {
      logger.error("Mark no-show error:", error);
      throw error;
    }
  }

  /**
   * Submit session feedback
   */
  static async submitFeedback(
    sessionId: string,
    userId: string,
    feedback: SessionFeedback
  ): Promise<ISession> {
    try {
      const session = await Session.findById(sessionId);

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (session.status !== "completed") {
        throw createError(
          "Can only provide feedback for completed sessions",
          400
        );
      }

      // Verify user authorization
      const isClient = session.userId.toString() === userId;
      const isCounselor = session.counselorId.toString() === userId;

      if (!isClient && !isCounselor) {
        throw createError(
          "Unauthorized to provide feedback for this session",
          403
        );
      }

      // Add feedback
      const feedbackData = {
        ...feedback,
        submittedBy: userId as any,
        submittedAt: new Date(),
        role: isClient ? "client" : "counselor",
      };

      if (!session.feedback) {
        session.feedback = [];
      }

      session.feedback.push(feedbackData as any);

      // Update overall rating if client feedback
      if (isClient) {
        session.outcome = {
          ...session.outcome,
          rating: feedback.rating,
          feedback: feedback.feedback,
        };
      }

      await session.save();

      // Update counselor statistics with new rating
      if (isClient) {
        await CounselorStatsService.updateSessionStats(
          session.counselorId.toString(),
          {
            earnings: 0, // No additional earnings
            rating: feedback.rating,
            completed: false, // Don't increment session count
          }
        );
      }

      logger.info(`Feedback submitted for session ${sessionId} by ${userId}`);
      return session;
    } catch (error) {
      logger.error("Submit feedback error:", error);
      throw error;
    }
  }

  /**
   * Get session statistics
   */
  static async getSessionStats(filters: any = {}): Promise<SessionStats> {
    try {
      const matchStage: any = {};

      if (filters.counselorId) matchStage.counselorId = filters.counselorId;
      if (filters.userId) matchStage.userId = filters.userId;
      if (filters.dateFrom || filters.dateTo) {
        matchStage.scheduledAt = {};
        if (filters.dateFrom)
          matchStage.scheduledAt.$gte = new Date(filters.dateFrom);
        if (filters.dateTo)
          matchStage.scheduledAt.$lte = new Date(filters.dateTo);
      }

      const stats = await Session.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            totalSessions: { $sum: 1 },
            completedSessions: {
              $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
            },
            cancelledSessions: {
              $sum: { $cond: [{ $eq: ["$status", "cancelled"] }, 1, 0] },
            },
            noShowSessions: {
              $sum: { $cond: [{ $eq: ["$status", "no-show"] }, 1, 0] },
            },
            averageRating: { $avg: "$outcome.rating" },
            averageDuration: { $avg: "$actualDuration" },
            totalRatings: {
              $sum: { $cond: [{ $ne: ["$outcome.rating", null] }, 1, 0] },
            },
          },
        },
      ]);

      const result = stats[0] || {
        totalSessions: 0,
        completedSessions: 0,
        cancelledSessions: 0,
        noShowSessions: 0,
        averageRating: 0,
        averageDuration: 0,
      };

      const completionRate =
        result.totalSessions > 0
          ? (result.completedSessions / result.totalSessions) * 100
          : 0;

      return {
        totalSessions: result.totalSessions,
        completedSessions: result.completedSessions,
        cancelledSessions: result.cancelledSessions,
        noShowSessions: result.noShowSessions,
        averageRating: result.averageRating || 0,
        averageDuration: result.averageDuration || 0,
        completionRate,
      };
    } catch (error) {
      logger.error("Get session stats error:", error);
      throw error;
    }
  }

  /**
   * Get upcoming sessions
   */
  static async getUpcomingSessions(
    userId: string,
    role: "client" | "counselor"
  ): Promise<ISession[]> {
    try {
      const query: any = {
        status: "scheduled",
        scheduledAt: { $gte: new Date() },
      };

      if (role === "client") {
        query.userId = userId;
      } else {
        query.counselorId = userId;
      }

      const sessions = await Session.find(query)
        .populate("userId", "firstName lastName email profilePicture")
        .populate("counselorId", "userId specializations")
        .sort({ scheduledAt: 1 })
        .limit(10);

      return sessions;
    } catch (error) {
      logger.error("Get upcoming sessions error:", error);
      throw error;
    }
  }

  /**
   * Send session reminder notifications
   */
  static async sendSessionReminders(): Promise<void> {
    try {
      // Find sessions starting in 1 hour
      const oneHourFromNow = new Date(Date.now() + 60 * 60 * 1000);
      const fiftyMinutesFromNow = new Date(Date.now() + 50 * 60 * 1000);

      const upcomingSessions = await Session.find({
        status: "scheduled",
        scheduledAt: {
          $gte: fiftyMinutesFromNow,
          $lte: oneHourFromNow,
        },
        reminderSent: { $ne: true },
      })
        .populate("userId")
        .populate("counselorId");

      for (const session of upcomingSessions) {
        await this.sendSessionReminderNotifications(session);

        // Mark reminder as sent
        session.reminderSent = true;
        await session.save();
      }

      logger.info(`Sent reminders for ${upcomingSessions.length} sessions`);
    } catch (error) {
      logger.error("Send session reminders error:", error);
    }
  }

  /**
   * Handle no-show policy
   */
  private static async handleNoShowPolicy(session: ISession): Promise<void> {
    try {
      // No-show policy: Client is charged, counselor gets partial compensation
      const counselorCompensation = session.pricing.totalAmount * 0.5; // 50% compensation

      // Update counselor earnings
      await CounselorStatsService.updateSessionStats(
        session.counselorId.toString(),
        {
          earnings: counselorCompensation,
          completed: false,
        }
      );

      logger.info(`No-show policy applied for session ${session._id}`);
    } catch (error) {
      logger.error("Handle no-show policy error:", error);
    }
  }

  /**
   * Send session started notifications
   */
  private static async sendSessionStartedNotifications(
    session: ISession
  ): Promise<void> {
    try {
      const client = session.userId as any;
      const counselor = session.counselorId as any;

      // Notify both parties
      const recipients = [
        { email: client.email, name: client.firstName, role: "client" },
        {
          email: counselor.userId.email,
          name: counselor.userId.firstName,
          role: "counselor",
        },
      ];

      for (const recipient of recipients) {
        await sendEmail({
          to: recipient.email,
          subject: "Session Started - Theramea",
          html: `
            <h2>Session Started</h2>
            <p>Hi ${recipient.name},</p>
            <p>Your therapy session has started. Please join the video call.</p>
            <p><strong>Session Details:</strong></p>
            <ul>
              <li>Duration: ${session.duration} minutes</li>
              <li>Started: ${session.startedAt?.toLocaleString()}</li>
            </ul>
            <p>Best regards,<br>The Theramea Team</p>
          `,
        });
      }
    } catch (error) {
      logger.error("Send session started notifications error:", error);
    }
  }

  /**
   * Send session completed notifications
   */
  private static async sendSessionCompletedNotifications(
    session: ISession
  ): Promise<void> {
    try {
      const client = session.userId as any;
      const counselor = session.counselorId as any;

      // Send completion notification to client
      await sendEmail({
        to: client.email,
        subject: "Session Completed - Please Provide Feedback",
        html: `
          <h2>Session Completed</h2>
          <p>Hi ${client.firstName},</p>
          <p>Your therapy session has been completed successfully.</p>
          <p><strong>Session Summary:</strong></p>
          <ul>
            <li>Duration: ${session.actualDuration} minutes</li>
            <li>Completed: ${session.endedAt?.toLocaleString()}</li>
          </ul>
          <p>Please take a moment to provide feedback about your session.</p>
          <p>Best regards,<br>The Theramea Team</p>
        `,
      });

      // Send completion notification to counselor
      await sendEmail({
        to: counselor.userId.email,
        subject: "Session Completed - Theramea",
        html: `
          <h2>Session Completed</h2>
          <p>Hi ${counselor.userId.firstName},</p>
          <p>Your therapy session with ${
            client.firstName
          } has been completed.</p>
          <p><strong>Session Summary:</strong></p>
          <ul>
            <li>Duration: ${session.actualDuration} minutes</li>
            <li>Completed: ${session.endedAt?.toLocaleString()}</li>
            <li>Earnings: ${session.pricing.currency} ${
          session.pricing.totalAmount - session.pricing.platformFee
        }</li>
          </ul>
          <p>Thank you for providing quality care.</p>
          <p>Best regards,<br>The Theramea Team</p>
        `,
      });
    } catch (error) {
      logger.error("Send session completed notifications error:", error);
    }
  }

  /**
   * Send no-show notifications
   */
  private static async sendNoShowNotifications(
    session: ISession,
    reason: string
  ): Promise<void> {
    try {
      const client = session.userId as any;
      const counselor = session.counselorId as any;

      // Notify both parties
      const recipients = [
        { email: client.email, name: client.firstName, role: "client" },
        {
          email: counselor.userId.email,
          name: counselor.userId.firstName,
          role: "counselor",
        },
      ];

      for (const recipient of recipients) {
        await sendEmail({
          to: recipient.email,
          subject: "Session No-Show - Theramea",
          html: `
            <h2>Session No-Show</h2>
            <p>Hi ${recipient.name},</p>
            <p>The session scheduled for ${session.scheduledAt.toLocaleString()} was marked as a no-show.</p>
            <p><strong>Reason:</strong> ${reason}</p>
            ${
              recipient.role === "client"
                ? "<p>You have been charged according to our no-show policy.</p>"
                : "<p>You will receive partial compensation for this session.</p>"
            }
            <p>Best regards,<br>The Theramea Team</p>
          `,
        });
      }
    } catch (error) {
      logger.error("Send no-show notifications error:", error);
    }
  }

  /**
   * Send session reminder notifications
   */
  private static async sendSessionReminderNotifications(
    session: ISession
  ): Promise<void> {
    try {
      const client = session.userId as any;
      const counselor = session.counselorId as any;

      // Notify both parties
      const recipients = [
        { email: client.email, name: client.firstName, role: "client" },
        {
          email: counselor.userId.email,
          name: counselor.userId.firstName,
          role: "counselor",
        },
      ];

      for (const recipient of recipients) {
        await sendEmail({
          to: recipient.email,
          subject: "Session Reminder - Starting in 1 Hour",
          html: `
            <h2>Session Reminder</h2>
            <p>Hi ${recipient.name},</p>
            <p>This is a reminder that your therapy session is starting in 1 hour.</p>
            <p><strong>Session Details:</strong></p>
            <ul>
              <li>Time: ${session.scheduledAt.toLocaleString()}</li>
              <li>Duration: ${session.duration} minutes</li>
              <li>${recipient.role === "client" ? "Counselor" : "Client"}: ${
            recipient.role === "client"
              ? counselor.userId.firstName
              : client.firstName
          }</li>
            </ul>
            <p>Please be ready to join the video call at the scheduled time.</p>
            <p>Best regards,<br>The Theramea Team</p>
          `,
        });
      }
    } catch (error) {
      logger.error("Send session reminder notifications error:", error);
    }
  }
}
