'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import Header from '@/components/layout/Header';
import ChatRoomList from '@/components/chat/ChatRoomList';

export default function ChatRoomsPage() {
  const router = useRouter();
  const { isAuthenticated, isGuest, checkAuth, isLoading } = useAuthStore();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isGuest && !isLoading) {
      router.push('/');
    }
  }, [isAuthenticated, isGuest, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!isAuthenticated && !isGuest) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Chat Rooms</h1>
          <p className="text-gray-600">
            Join supportive communities and connect with others on similar journeys.
          </p>
        </div>

        {/* Guest Notice */}
        {isGuest && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-blue-900 mb-2">
                  Browsing as Guest
                </h3>
                <p className="text-blue-800 mb-4">
                  You can view and join chat rooms as a guest, but creating an account will give you access to additional features like message history, saved conversations, and personalized recommendations.
                </p>
                <div className="flex flex-col sm:flex-row gap-3">
                  <a
                    href="/auth/register"
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-center font-medium"
                  >
                    Create Free Account
                  </a>
                  <a
                    href="/auth/login"
                    className="border border-blue-300 text-blue-700 px-4 py-2 rounded-md hover:bg-blue-50 transition-colors text-center font-medium"
                  >
                    Sign In
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Chat Room Guidelines */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Community Guidelines</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">✅ Do:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Be respectful and supportive</li>
                <li>• Share experiences and insights</li>
                <li>• Listen actively to others</li>
                <li>• Follow room-specific rules</li>
                <li>• Report inappropriate behavior</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">❌ Don't:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Share personal contact information</li>
                <li>• Give medical or legal advice</li>
                <li>• Use offensive or discriminatory language</li>
                <li>• Spam or self-promote</li>
                <li>• Share graphic or triggering content</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Chat Room List */}
        <ChatRoomList />
      </main>
    </div>
  );
}
