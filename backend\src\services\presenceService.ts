import { redisClient } from '@/config/redis';
import { logger } from '@/utils/logger';

export interface UserPresence {
  userId?: string;
  anonymousId?: string;
  displayName: string;
  isAnonymous: boolean;
  currentRoom?: string;
  lastSeen: Date;
  socketId: string;
}

export interface RoomPresence {
  roomId: string;
  participants: UserPresence[];
  totalCount: number;
  anonymousCount: number;
  registeredCount: number;
}

export class PresenceService {
  private static readonly USER_PRESENCE_PREFIX = 'presence:user:';
  private static readonly ROOM_PRESENCE_PREFIX = 'presence:room:';
  private static readonly SOCKET_USER_PREFIX = 'socket:user:';
  private static readonly USER_SOCKET_PREFIX = 'user:socket:';

  /**
   * Set user online status
   */
  static async setUserOnline(presence: UserPresence): Promise<void> {
    try {
      if (!redisClient) {
        logger.warn('Redis not available, skipping presence update');
        return;
      }

      const userKey = presence.userId 
        ? `${this.USER_PRESENCE_PREFIX}${presence.userId}`
        : `${this.USER_PRESENCE_PREFIX}anon:${presence.anonymousId}`;

      const presenceData = {
        ...presence,
        lastSeen: presence.lastSeen.toISOString(),
        isOnline: true
      };

      // Set user presence with 5-minute expiry
      await redisClient.setEx(userKey, 300, JSON.stringify(presenceData));

      // Map socket to user
      await redisClient.setEx(
        `${this.SOCKET_USER_PREFIX}${presence.socketId}`,
        300,
        JSON.stringify(presence)
      );

      // Map user to socket
      if (presence.userId) {
        await redisClient.setEx(
          `${this.USER_SOCKET_PREFIX}${presence.userId}`,
          300,
          presence.socketId
        );
      }

      logger.debug(`User presence set: ${presence.displayName}`);
    } catch (error) {
      logger.error('Set user online error:', error);
    }
  }

  /**
   * Set user offline
   */
  static async setUserOffline(socketId: string): Promise<void> {
    try {
      if (!redisClient) {
        logger.warn('Redis not available, skipping presence update');
        return;
      }

      // Get user data from socket mapping
      const socketUserData = await redisClient.get(`${this.SOCKET_USER_PREFIX}${socketId}`);
      
      if (socketUserData) {
        const presence: UserPresence = JSON.parse(socketUserData);
        
        // Remove from room if present
        if (presence.currentRoom) {
          await this.removeUserFromRoom(presence.currentRoom, socketId);
        }

        // Remove user presence
        const userKey = presence.userId 
          ? `${this.USER_PRESENCE_PREFIX}${presence.userId}`
          : `${this.USER_PRESENCE_PREFIX}anon:${presence.anonymousId}`;

        await redisClient.del(userKey);

        // Remove socket mappings
        await redisClient.del(`${this.SOCKET_USER_PREFIX}${socketId}`);
        
        if (presence.userId) {
          await redisClient.del(`${this.USER_SOCKET_PREFIX}${presence.userId}`);
        }

        logger.debug(`User presence removed: ${presence.displayName}`);
      }
    } catch (error) {
      logger.error('Set user offline error:', error);
    }
  }

  /**
   * Add user to room
   */
  static async addUserToRoom(roomId: string, socketId: string): Promise<void> {
    try {
      if (!redisClient) {
        logger.warn('Redis not available, skipping room presence update');
        return;
      }

      // Get user data
      const socketUserData = await redisClient.get(`${this.SOCKET_USER_PREFIX}${socketId}`);
      
      if (!socketUserData) {
        logger.warn(`No user data found for socket: ${socketId}`);
        return;
      }

      const presence: UserPresence = JSON.parse(socketUserData);
      presence.currentRoom = roomId;

      // Update user presence with room info
      const userKey = presence.userId 
        ? `${this.USER_PRESENCE_PREFIX}${presence.userId}`
        : `${this.USER_PRESENCE_PREFIX}anon:${presence.anonymousId}`;

      await redisClient.setEx(userKey, 300, JSON.stringify({
        ...presence,
        lastSeen: new Date().toISOString(),
        isOnline: true
      }));

      // Update socket mapping
      await redisClient.setEx(
        `${this.SOCKET_USER_PREFIX}${socketId}`,
        300,
        JSON.stringify(presence)
      );

      // Add to room participants
      const roomKey = `${this.ROOM_PRESENCE_PREFIX}${roomId}`;
      await redisClient.sAdd(roomKey, socketId);
      await redisClient.expire(roomKey, 3600); // 1 hour expiry

      logger.debug(`User ${presence.displayName} added to room ${roomId}`);
    } catch (error) {
      logger.error('Add user to room error:', error);
    }
  }

  /**
   * Remove user from room
   */
  static async removeUserFromRoom(roomId: string, socketId: string): Promise<void> {
    try {
      if (!redisClient) {
        logger.warn('Redis not available, skipping room presence update');
        return;
      }

      // Get user data
      const socketUserData = await redisClient.get(`${this.SOCKET_USER_PREFIX}${socketId}`);
      
      if (socketUserData) {
        const presence: UserPresence = JSON.parse(socketUserData);
        presence.currentRoom = undefined;

        // Update user presence
        const userKey = presence.userId 
          ? `${this.USER_PRESENCE_PREFIX}${presence.userId}`
          : `${this.USER_PRESENCE_PREFIX}anon:${presence.anonymousId}`;

        await redisClient.setEx(userKey, 300, JSON.stringify({
          ...presence,
          lastSeen: new Date().toISOString(),
          isOnline: true
        }));

        // Update socket mapping
        await redisClient.setEx(
          `${this.SOCKET_USER_PREFIX}${socketId}`,
          300,
          JSON.stringify(presence)
        );

        logger.debug(`User ${presence.displayName} removed from room ${roomId}`);
      }

      // Remove from room participants
      const roomKey = `${this.ROOM_PRESENCE_PREFIX}${roomId}`;
      await redisClient.sRem(roomKey, socketId);
    } catch (error) {
      logger.error('Remove user from room error:', error);
    }
  }

  /**
   * Get room presence
   */
  static async getRoomPresence(roomId: string): Promise<RoomPresence> {
    try {
      if (!redisClient) {
        logger.warn('Redis not available, returning empty room presence');
        return {
          roomId,
          participants: [],
          totalCount: 0,
          anonymousCount: 0,
          registeredCount: 0
        };
      }

      const roomKey = `${this.ROOM_PRESENCE_PREFIX}${roomId}`;
      const socketIds = await redisClient.sMembers(roomKey);

      const participants: UserPresence[] = [];
      let anonymousCount = 0;
      let registeredCount = 0;

      for (const socketId of socketIds) {
        const socketUserData = await redisClient.get(`${this.SOCKET_USER_PREFIX}${socketId}`);
        
        if (socketUserData) {
          const presence: UserPresence = JSON.parse(socketUserData);
          participants.push(presence);

          if (presence.isAnonymous) {
            anonymousCount++;
          } else {
            registeredCount++;
          }
        }
      }

      return {
        roomId,
        participants,
        totalCount: participants.length,
        anonymousCount,
        registeredCount
      };
    } catch (error) {
      logger.error('Get room presence error:', error);
      return {
        roomId,
        participants: [],
        totalCount: 0,
        anonymousCount: 0,
        registeredCount: 0
      };
    }
  }

  /**
   * Get user presence
   */
  static async getUserPresence(userId: string): Promise<UserPresence | null> {
    try {
      if (!redisClient) {
        logger.warn('Redis not available, returning null user presence');
        return null;
      }

      const userKey = `${this.USER_PRESENCE_PREFIX}${userId}`;
      const presenceData = await redisClient.get(userKey);

      if (presenceData) {
        const presence = JSON.parse(presenceData);
        presence.lastSeen = new Date(presence.lastSeen);
        return presence;
      }

      return null;
    } catch (error) {
      logger.error('Get user presence error:', error);
      return null;
    }
  }

  /**
   * Get online users count
   */
  static async getOnlineUsersCount(): Promise<{ total: number; registered: number; anonymous: number }> {
    try {
      if (!redisClient) {
        logger.warn('Redis not available, returning zero counts');
        return { total: 0, registered: 0, anonymous: 0 };
      }

      const pattern = `${this.USER_PRESENCE_PREFIX}*`;
      const keys = await redisClient.keys(pattern);

      let registered = 0;
      let anonymous = 0;

      for (const key of keys) {
        if (key.includes(':anon:')) {
          anonymous++;
        } else {
          registered++;
        }
      }

      return {
        total: registered + anonymous,
        registered,
        anonymous
      };
    } catch (error) {
      logger.error('Get online users count error:', error);
      return { total: 0, registered: 0, anonymous: 0 };
    }
  }

  /**
   * Cleanup expired presence data
   */
  static async cleanupExpiredPresence(): Promise<void> {
    try {
      if (!redisClient) {
        logger.warn('Redis not available, skipping presence cleanup');
        return;
      }

      // This is handled automatically by Redis TTL, but we can add custom cleanup logic here
      logger.debug('Presence cleanup completed');
    } catch (error) {
      logger.error('Cleanup expired presence error:', error);
    }
  }

  /**
   * Get user's socket ID
   */
  static async getUserSocketId(userId: string): Promise<string | null> {
    try {
      if (!redisClient) {
        return null;
      }

      return await redisClient.get(`${this.USER_SOCKET_PREFIX}${userId}`);
    } catch (error) {
      logger.error('Get user socket ID error:', error);
      return null;
    }
  }

  /**
   * Check if user is online
   */
  static async isUserOnline(userId: string): Promise<boolean> {
    try {
      if (!redisClient) {
        return false;
      }

      const userKey = `${this.USER_PRESENCE_PREFIX}${userId}`;
      const exists = await redisClient.exists(userKey);
      return exists === 1;
    } catch (error) {
      logger.error('Check user online error:', error);
      return false;
    }
  }
}
