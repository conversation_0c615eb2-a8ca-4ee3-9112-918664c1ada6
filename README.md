# Theramea - Digital Counseling Platform

Theramea is a digital platform that makes it easy for people to access professional counseling without the pressure, judgment, or complexity typically associated with traditional therapy.

## Features

### For Users
- **Anonymous Access**: Browse resources and view chatrooms as a guest
- **Safe Group Chats**: Join topic-specific chatrooms for peer support
- **Self-Help Library**: Access articles, videos, and tools for personal growth
- **Professional Sessions**: Book 1-on-1 video sessions with certified counselors
- **Flexible Engagement**: Start as a guest, upgrade anytime

### For Counselors
- **Flexible Scheduling**: Set your own availability and manage bookings
- **Remote Sessions**: Conduct sessions through integrated video platform
- **Earnings Dashboard**: Track sessions and manage payouts
- **Professional Profile**: Showcase expertise and build client relationships

### For Administrators
- **Counselor Management**: Review and approve counselor applications
- **Content Moderation**: Manage resources and monitor chatrooms
- **System Oversight**: Handle reports and maintain platform quality

## Tech Stack

### Frontend
- **Next.js 14** with TypeScript
- **TailwindCSS** for styling
- **Zustand** for state management
- **React Query** for API calls
- **Socket.io** for real-time features

### Backend
- **Node.js** with Express and TypeScript
- **MongoDB** for data storage
- **Redis** for real-time messaging and caching
- **JWT** for authentication
- **Paystack** for payment processing
- **Daily.co** for video sessions

## Project Structure

```
theramea/
├── frontend/          # Next.js frontend application
├── backend/           # Node.js/Express backend API
├── package.json       # Root package.json for workspace management
└── README.md         # This file
```

## Getting Started

### Prerequisites
- Node.js 18+ 
- MongoDB
- Redis
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd theramea
```

2. Install dependencies:
```bash
npm run install:all
```

3. Set up environment variables:
```bash
# Backend
cp backend/.env.example backend/.env
# Edit backend/.env with your configuration

# Frontend (if needed)
cp frontend/.env.example frontend/.env.local
```

4. Start development servers:
```bash
npm run dev
```

This will start both frontend (http://localhost:3000) and backend (http://localhost:5000) servers.

### Available Scripts

- `npm run dev` - Start both frontend and backend in development mode
- `npm run build` - Build both applications for production
- `npm run start` - Start both applications in production mode
- `npm run test` - Run tests for both applications
- `npm run clean` - Clean all node_modules and build artifacts

## Development Workflow

1. **Database Setup**: Ensure MongoDB and Redis are running
2. **Environment Configuration**: Set up all required environment variables
3. **Development**: Use `npm run dev` for hot-reload development
4. **Testing**: Write and run tests with `npm run test`
5. **Building**: Use `npm run build` before deployment

## Contributing

1. Follow the existing code structure and patterns
2. Write tests for new features
3. Use TypeScript for type safety
4. Follow the established naming conventions
5. Update documentation as needed

## License

This project is licensed under the MIT License.
