@echo off
setlocal enabledelayedexpansion

REM 🐳 Theramea Docker Setup Script for Windows

echo 🐳 Setting up Theramea with Docker...

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed. Please install Docker Desktop from https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose is not installed. Please install Docker Compose
    pause
    exit /b 1
)

echo [SUCCESS] Docker and Docker Compose are installed

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker Desktop
    pause
    exit /b 1
)

echo [SUCCESS] Docker is running

REM Handle command line arguments
set "command=%1"
if "%command%"=="" set "command=start"

if "%command%"=="start" goto :start
if "%command%"=="stop" goto :stop
if "%command%"=="restart" goto :restart
if "%command%"=="logs" goto :logs
if "%command%"=="status" goto :status
if "%command%"=="clean" goto :clean
if "%command%"=="rebuild" goto :rebuild
goto :usage

:start
echo [INFO] Starting Theramea with Docker
echo ================================

echo [INFO] Cleaning up existing containers...
docker-compose down --volumes --remove-orphans >nul 2>&1
docker rm -f theramea-mongodb theramea-redis theramea-backend theramea-frontend theramea-nginx >nul 2>&1
docker volume rm theramea_mongodb_data theramea_redis_data theramea_backend_uploads >nul 2>&1
echo [SUCCESS] Cleanup completed

echo [INFO] Building and starting services...
docker-compose up --build -d
if errorlevel 1 (
    echo [ERROR] Failed to start services
    pause
    exit /b 1
)
echo [SUCCESS] All services started successfully

echo [INFO] Waiting for services to be ready...
timeout /t 10 /nobreak >nul

echo [INFO] Creating test data...
docker cp create-test-data.js theramea-backend:/app/ >nul 2>&1
docker exec theramea-backend node create-test-data.js >nul 2>&1

echo.
echo [SUCCESS] 🎉 Theramea is now running!
echo.
echo Service URLs:
echo 🌐 Frontend:     http://localhost:3000
echo 🔧 Backend API:  http://localhost:5000/api
echo 🧪 Testing:      http://localhost:3000/testing
echo ⚡ Nginx Proxy:  http://localhost:80
echo.
echo Database Access:
echo 📊 MongoDB:      ****************************************************
echo 🔴 Redis:        redis://localhost:6379
echo.
echo Test Accounts:
echo 👤 User:         <EMAIL> / password123
echo 👨‍⚕️ Counselor:    <EMAIL> / password123
echo 👑 Admin:        <EMAIL> / password123
echo.
echo Next steps:
echo 1. Open http://localhost:3000 in your browser
echo 2. Try logging in with test accounts
echo 3. Run 'docker-setup.bat logs' to view logs
echo 4. Run 'docker-setup.bat stop' to stop services
goto :end

:stop
echo [INFO] Stopping Theramea services...
docker-compose down
echo [SUCCESS] Services stopped
goto :end

:restart
echo [INFO] Restarting Theramea services...
docker-compose restart
echo [SUCCESS] Services restarted
goto :end

:logs
echo [INFO] Recent logs:
docker-compose logs --tail=50
goto :end

:status
echo [INFO] Service Status:
docker-compose ps
echo.
echo Service URLs:
echo 🌐 Frontend:     http://localhost:3000
echo 🔧 Backend API:  http://localhost:5000/api
echo 🧪 Testing:      http://localhost:3000/testing
goto :end

:clean
echo [INFO] Cleaning up all Theramea Docker resources...
docker-compose down --volumes --remove-orphans
docker system prune -f
echo [SUCCESS] Cleanup completed
goto :end

:rebuild
echo [INFO] Rebuilding and restarting services...
docker-compose down
docker-compose up --build -d
echo [SUCCESS] Rebuild completed
goto :end

:usage
echo Usage: docker-setup.bat {start^|stop^|restart^|logs^|status^|clean^|rebuild}
echo.
echo Commands:
echo   start    - Start all services (default)
echo   stop     - Stop all services
echo   restart  - Restart all services
echo   logs     - Show recent logs
echo   status   - Show service status
echo   clean    - Clean up all Docker resources
echo   rebuild  - Rebuild and restart services
goto :end

:end
pause
