'use client';

import { useState } from 'react';
import { ChatMessage } from '@/types/chat';
import { useAuthStore } from '@/store/authStore';

interface MessageItemProps {
  message: ChatMessage;
  onReply?: (message: ChatMessage) => void;
  onReport?: (messageId: string) => void;
  onReaction?: (messageId: string, emoji: string) => void;
}

export default function MessageItem({ message, onReply, onReport, onReaction }: MessageItemProps) {
  const { user, isGuest } = useAuthStore();
  const [showActions, setShowActions] = useState(false);
  const [showReactions, setShowReactions] = useState(false);

  const isOwnMessage = !isGuest && user && message.senderId === user._id;
  const isSystemMessage = message.content.type === 'system';

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getMessageContent = () => {
    switch (message.content.type) {
      case 'text':
        return message.content.text;
      case 'image':
        return (
          <div className="mt-2">
            <img
              src={message.content.fileUrl}
              alt={message.content.fileName || 'Shared image'}
              className="max-w-sm rounded-lg shadow-sm"
            />
          </div>
        );
      case 'file':
        return (
          <div className="mt-2 p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-gray-900">{message.content.fileName}</p>
                <p className="text-xs text-gray-500">
                  {message.content.fileSize ? `${(message.content.fileSize / 1024).toFixed(1)} KB` : ''}
                </p>
              </div>
            </div>
          </div>
        );
      case 'system':
        return (
          <div className="text-center py-2">
            <span className="text-sm text-gray-500 italic">{message.content.text}</span>
          </div>
        );
      default:
        return message.content.text;
    }
  };

  const commonReactions = ['👍', '❤️', '😊', '😢', '😮', '😡'];

  if (isSystemMessage) {
    return (
      <div className="flex justify-center my-4">
        <div className="bg-gray-100 rounded-full px-4 py-2">
          <span className="text-sm text-gray-600">{message.content.text}</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`group flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-4`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
        {/* Message Header */}
        <div className={`flex items-center space-x-2 mb-1 ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
          <span className="text-sm font-medium text-gray-900">
            {message.senderDisplayName}
          </span>
          <span className="text-xs text-gray-500">
            {formatTime(message.createdAt)}
          </span>
          {message.isEdited && (
            <span className="text-xs text-gray-400">(edited)</span>
          )}
        </div>

        {/* Reply Context */}
        {message.replyTo && (
          <div className="mb-2 p-2 bg-gray-50 rounded border-l-2 border-gray-300">
            <p className="text-xs text-gray-600">Replying to a message</p>
          </div>
        )}

        {/* Message Content */}
        <div
          className={`rounded-lg px-4 py-2 ${
            isOwnMessage
              ? 'bg-purple-600 text-white'
              : 'bg-white border border-gray-200 text-gray-900'
          }`}
        >
          {getMessageContent()}
        </div>

        {/* Reactions */}
        {message.reactions.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {message.reactions.map((reaction) => (
              <button
                key={reaction.emoji}
                onClick={() => onReaction?.(message._id, reaction.emoji)}
                className="inline-flex items-center space-x-1 bg-gray-100 hover:bg-gray-200 rounded-full px-2 py-1 text-xs transition-colors"
              >
                <span>{reaction.emoji}</span>
                <span className="text-gray-600">{reaction.count}</span>
              </button>
            ))}
          </div>
        )}

        {/* Message Actions */}
        {showActions && !isSystemMessage && (
          <div className={`flex items-center space-x-2 mt-2 ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
            <button
              onClick={() => setShowReactions(!showReactions)}
              className="text-gray-400 hover:text-gray-600 p-1 rounded"
              title="Add reaction"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
            
            <button
              onClick={() => onReply?.(message)}
              className="text-gray-400 hover:text-gray-600 p-1 rounded"
              title="Reply"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
              </svg>
            </button>

            {!isOwnMessage && (
              <button
                onClick={() => onReport?.(message._id)}
                className="text-gray-400 hover:text-red-600 p-1 rounded"
                title="Report"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </button>
            )}
          </div>
        )}

        {/* Reaction Picker */}
        {showReactions && (
          <div className="flex space-x-1 mt-2 p-2 bg-white border border-gray-200 rounded-lg shadow-sm">
            {commonReactions.map((emoji) => (
              <button
                key={emoji}
                onClick={() => {
                  onReaction?.(message._id, emoji);
                  setShowReactions(false);
                }}
                className="hover:bg-gray-100 p-1 rounded text-lg"
              >
                {emoji}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
