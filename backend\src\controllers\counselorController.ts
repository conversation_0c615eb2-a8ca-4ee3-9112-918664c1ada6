import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { CounselorService } from "@/services/counselorService";
import { AvailabilityService } from "@/services/availabilityService";
import { CounselorStatsService } from "@/services/counselorStatsService";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";
import { validateFile, FILE_TYPES, FILE_SIZE_LIMITS } from "@/utils/cloudinary";

export class CounselorController {
  /**
   * Register as a counselor
   */
  static async registerCounselor(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError("Validation failed", 400));
      }

      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const counselor = await CounselorService.registerCounselor(
        req.user._id.toString(),
        req.body
      );

      res.status(201).json({
        success: true,
        message:
          "Counselor registration submitted successfully. Your application is under review.",
        data: { counselor },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get counselor profile
   */
  static async getCounselorProfile(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { counselorId } = req.params;
      const counselor = await CounselorService.getCounselorProfile(counselorId);

      res.json({
        success: true,
        data: { counselor },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get current user's counselor profile
   */
  static async getMyCounselorProfile(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );

      res.json({
        success: true,
        data: { counselor },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update counselor profile
   */
  static async updateCounselorProfile(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError("Validation failed", 400));
      }

      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );
      const updatedCounselor = await CounselorService.updateCounselorProfile(
        counselor._id.toString(),
        req.body
      );

      res.json({
        success: true,
        message: "Counselor profile updated successfully",
        data: { counselor: updatedCounselor },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update availability
   */
  static async updateAvailability(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError("Validation failed", 400));
      }

      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );
      const updatedCounselor = await CounselorService.updateAvailability(
        counselor._id.toString(),
        req.body
      );

      res.json({
        success: true,
        message: "Availability updated successfully",
        data: { availability: updatedCounselor.availability },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Upload verification documents
   */
  static async uploadVerificationDocuments(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const files = req.files as { [fieldname: string]: Express.Multer.File[] };

      if (!files.idDocument || !files.certificates) {
        return next(
          createError("ID document and certificates are required", 400)
        );
      }

      const idDocument = files.idDocument[0];
      const certificates = files.certificates;

      // Validate files
      validateFile(
        idDocument,
        [...FILE_TYPES.IMAGES, ...FILE_TYPES.DOCUMENTS],
        FILE_SIZE_LIMITS.DOCUMENT
      );
      certificates.forEach((cert) => {
        validateFile(
          cert,
          [...FILE_TYPES.IMAGES, ...FILE_TYPES.DOCUMENTS],
          FILE_SIZE_LIMITS.DOCUMENT
        );
      });

      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );
      const updatedCounselor =
        await CounselorService.uploadVerificationDocuments(
          counselor._id.toString(),
          idDocument,
          certificates
        );

      res.json({
        success: true,
        message: "Verification documents uploaded successfully",
        data: { verification: updatedCounselor.verification },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get counselors list (public)
   */
  static async getCounselors(req: Request, res: Response, next: NextFunction) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "statistics.averageRating",
        sortOrder = "desc",
        specializations,
        minRating,
        maxRate,
        currency,
        languages,
        sessionTypes,
      } = req.query;

      const filters: any = { verificationStatus: "approved" };

      if (specializations) {
        filters.specializations = Array.isArray(specializations)
          ? specializations
          : [specializations];
      }
      if (minRating) filters.minRating = parseFloat(minRating as string);
      if (maxRate) filters.maxRate = parseFloat(maxRate as string);
      if (currency) filters.currency = currency;
      if (languages) {
        filters.languages = Array.isArray(languages) ? languages : [languages];
      }
      if (sessionTypes) {
        filters.sessionTypes = Array.isArray(sessionTypes)
          ? sessionTypes
          : [sessionTypes];
      }

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as "asc" | "desc",
      };

      const result = await CounselorService.getCounselors(filters, options);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get pending counselor applications (admin only)
   */
  static async getPendingApplications(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "verification.submittedAt",
        sortOrder = "desc",
      } = req.query;

      const filters = { verificationStatus: "pending" };
      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as "asc" | "desc",
      };

      const result = await CounselorService.getCounselors(filters, options);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Approve counselor application (admin only)
   */
  static async approveCounselor(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { counselorId } = req.params;
      const counselor = await CounselorService.approveCounselor(
        counselorId,
        req.user._id.toString()
      );

      res.json({
        success: true,
        message: "Counselor approved successfully",
        data: { counselor },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reject counselor application (admin only)
   */
  static async rejectCounselor(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { counselorId } = req.params;
      const { reason } = req.body;

      if (!reason) {
        return next(createError("Rejection reason is required", 400));
      }

      const counselor = await CounselorService.rejectCounselor(
        counselorId,
        req.user._id.toString(),
        reason
      );

      res.json({
        success: true,
        message: "Counselor application rejected",
        data: { counselor },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get counselor statistics (for counselor dashboard)
   */
  static async getCounselorStats(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );

      // TODO: Implement session statistics when session service is ready
      const stats = {
        totalSessions: counselor.statistics.totalSessions,
        totalEarnings: counselor.statistics.totalEarnings,
        averageRating: counselor.statistics.averageRating,
        totalReviews: counselor.statistics.totalReviews,
        completionRate: counselor.statistics.completionRate,
        responseTime: counselor.statistics.responseTime,
        verificationStatus: counselor.verification.status,
        isAcceptingClients: counselor.settings.acceptingNewClients,
      };

      res.json({
        success: true,
        data: { stats },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update counselor settings
   */
  static async updateSettings(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );

      // Update settings
      const allowedSettings = [
        "acceptingNewClients",
        "autoAcceptBookings",
        "requiresApproval",
        "cancellationPolicy",
        "reschedulePolicy",
      ];

      allowedSettings.forEach((setting) => {
        if (req.body[setting] !== undefined) {
          counselor.settings[setting as keyof typeof counselor.settings] =
            req.body[setting];
        }
      });

      await counselor.save();

      res.json({
        success: true,
        message: "Settings updated successfully",
        data: { settings: counselor.settings },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get available time slots for a counselor
   */
  static async getAvailableSlots(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { counselorId } = req.params;
      const { startDate, endDate, duration = 60 } = req.query;

      if (!startDate) {
        return next(createError("Start date is required", 400));
      }

      const start = new Date(startDate as string);
      const end = endDate
        ? new Date(endDate as string)
        : new Date(start.getTime() + 7 * 24 * 60 * 60 * 1000); // Default to 7 days

      const availableSlots = await AvailabilityService.getAvailableSlots({
        counselorId,
        startDate: start,
        endDate: end,
        duration: parseInt(duration as string),
      });

      res.json({
        success: true,
        data: { availableSlots },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get counselor's weekly availability
   */
  static async getWeeklyAvailability(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { counselorId } = req.params;

      const weeklyAvailability =
        await AvailabilityService.getWeeklyAvailability(counselorId);

      res.json({
        success: true,
        data: weeklyAvailability,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Block time slots
   */
  static async blockTimeSlots(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { dates, reason } = req.body;

      if (!dates || !Array.isArray(dates)) {
        return next(createError("Dates array is required", 400));
      }

      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );

      await AvailabilityService.blockTimeSlots(
        counselor._id.toString(),
        dates.map((date: string) => new Date(date)),
        reason
      );

      res.json({
        success: true,
        message: "Time slots blocked successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Unblock time slots
   */
  static async unblockTimeSlots(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { dates } = req.body;

      if (!dates || !Array.isArray(dates)) {
        return next(createError("Dates array is required", 400));
      }

      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );

      await AvailabilityService.unblockTimeSlots(
        counselor._id.toString(),
        dates.map((date: string) => new Date(date))
      );

      res.json({
        success: true,
        message: "Time slots unblocked successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get detailed counselor statistics
   */
  static async getDetailedStats(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );
      const stats = await CounselorStatsService.getCounselorStats(
        counselor._id.toString()
      );

      res.json({
        success: true,
        data: { stats },
      });
    } catch (error) {
      next(error);
    }
  }
}
