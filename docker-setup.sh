#!/bin/bash

# 🐳 Theramea Docker Setup Script

echo "🐳 Setting up Theramea with Docker..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker Desktop from https://www.docker.com/products/docker-desktop"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose"
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Check if Dock<PERSON> is running
check_docker_running() {
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker Desktop"
        exit 1
    fi
    
    print_success "Docker is running"
}

# Stop and remove existing containers
cleanup_existing() {
    print_status "Cleaning up existing containers..."
    
    docker-compose down --volumes --remove-orphans 2>/dev/null || true
    
    # Remove existing containers if they exist
    docker rm -f theramea-mongodb theramea-redis theramea-backend theramea-frontend theramea-nginx 2>/dev/null || true
    
    # Remove existing volumes
    docker volume rm theramea_mongodb_data theramea_redis_data theramea_backend_uploads 2>/dev/null || true
    
    print_success "Cleanup completed"
}

# Build and start services
start_services() {
    print_status "Building and starting services..."
    
    # Build and start all services
    docker-compose up --build -d
    
    if [ $? -eq 0 ]; then
        print_success "All services started successfully"
    else
        print_error "Failed to start services"
        exit 1
    fi
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for MongoDB
    print_status "Waiting for MongoDB..."
    until docker exec theramea-mongodb mongosh --eval "db.runCommand('ping')" &> /dev/null; do
        sleep 2
    done
    print_success "MongoDB is ready"
    
    # Wait for Redis
    print_status "Waiting for Redis..."
    until docker exec theramea-redis redis-cli ping &> /dev/null; do
        sleep 2
    done
    print_success "Redis is ready"
    
    # Wait for Backend
    print_status "Waiting for Backend API..."
    until curl -f http://localhost:5000/api/health &> /dev/null; do
        sleep 5
    done
    print_success "Backend API is ready"
    
    # Wait for Frontend
    print_status "Waiting for Frontend..."
    until curl -f http://localhost:3000 &> /dev/null; do
        sleep 5
    done
    print_success "Frontend is ready"
}

# Create test data
create_test_data() {
    print_status "Creating test data..."
    
    # Copy test data script to backend container and run it
    docker cp create-test-data.js theramea-backend:/app/
    docker exec theramea-backend node create-test-data.js
    
    if [ $? -eq 0 ]; then
        print_success "Test data created successfully"
    else
        print_warning "Failed to create test data (this is optional)"
    fi
}

# Show service status
show_status() {
    print_status "Service Status:"
    echo ""
    docker-compose ps
    echo ""
    
    print_status "Service URLs:"
    echo "🌐 Frontend:     http://localhost:3000"
    echo "🔧 Backend API:  http://localhost:5000/api"
    echo "🧪 Testing:      http://localhost:3000/testing"
    echo "⚡ Nginx Proxy:  http://localhost:80"
    echo ""
    
    print_status "Database Access:"
    echo "📊 MongoDB:      ****************************************************"
    echo "🔴 Redis:        redis://localhost:6379"
    echo ""
    
    print_status "Test Accounts:"
    echo "👤 User:         <EMAIL> / password123"
    echo "👨‍⚕️ Counselor:    <EMAIL> / password123"
    echo "👑 Admin:        <EMAIL> / password123"
}

# Show logs
show_logs() {
    if [ "$1" = "follow" ]; then
        print_status "Following logs (Ctrl+C to stop)..."
        docker-compose logs -f
    else
        print_status "Recent logs:"
        docker-compose logs --tail=50
    fi
}

# Main function
main() {
    case "${1:-start}" in
        "start")
            echo "🐳 Starting Theramea with Docker"
            echo "================================"
            
            check_docker
            check_docker_running
            cleanup_existing
            start_services
            wait_for_services
            create_test_data
            show_status
            
            echo ""
            print_success "🎉 Theramea is now running!"
            echo ""
            echo "Next steps:"
            echo "1. Open http://localhost:3000 in your browser"
            echo "2. Try logging in with test accounts"
            echo "3. Run './docker-setup.sh logs' to view logs"
            echo "4. Run './docker-setup.sh stop' to stop services"
            ;;
            
        "stop")
            print_status "Stopping Theramea services..."
            docker-compose down
            print_success "Services stopped"
            ;;
            
        "restart")
            print_status "Restarting Theramea services..."
            docker-compose restart
            print_success "Services restarted"
            ;;
            
        "logs")
            show_logs "${2:-recent}"
            ;;
            
        "status")
            show_status
            ;;
            
        "clean")
            print_status "Cleaning up all Theramea Docker resources..."
            cleanup_existing
            docker system prune -f
            print_success "Cleanup completed"
            ;;
            
        "rebuild")
            print_status "Rebuilding and restarting services..."
            docker-compose down
            docker-compose up --build -d
            wait_for_services
            show_status
            print_success "Rebuild completed"
            ;;
            
        *)
            echo "Usage: $0 {start|stop|restart|logs|status|clean|rebuild}"
            echo ""
            echo "Commands:"
            echo "  start    - Start all services (default)"
            echo "  stop     - Stop all services"
            echo "  restart  - Restart all services"
            echo "  logs     - Show recent logs"
            echo "  status   - Show service status"
            echo "  clean    - Clean up all Docker resources"
            echo "  rebuild  - Rebuild and restart services"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
