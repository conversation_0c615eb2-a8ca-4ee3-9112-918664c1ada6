import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { AuthService } from "@/services/authService";
import { JWTService } from "@/utils/jwt";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";
import { sendWelcomeEmail, sendPasswordResetSuccessEmail } from "@/utils/email";
import { IUser } from "@/models/User";
// Import auth middleware to ensure Request type extension is available
import "@/middleware/auth";

export class AuthController {
  /**
   * Register a new user
   */
  static async register(req: Request, res: Response, next: NextFunction) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError("Validation failed", 400));
      }

      const {
        firstName,
        lastName,
        username,
        email,
        password,
        areasOfInterest,
      } = req.body;

      const result = await AuthService.register({
        firstName,
        lastName,
        username,
        email,
        password,
        areasOfInterest,
      });

      // Send welcome email (don't wait for it)
      sendWelcomeEmail(result.user.email!, result.user.firstName!).catch(
        (error) => logger.error("Failed to send welcome email:", error)
      );

      res.status(201).json({
        success: true,
        message:
          "User registered successfully. Please check your email to verify your account.",
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Login user
   */
  static async login(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError("Validation failed", 400));
      }

      const { email, password } = req.body;

      const result = await AuthService.login({ email, password });

      res.json({
        success: true,
        message: "Login successful",
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(req: Request, res: Response, next: NextFunction) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return next(createError("Refresh token is required", 400));
      }

      const tokens = await AuthService.refreshToken(refreshToken);

      res.json({
        success: true,
        message: "Token refreshed successfully",
        data: { tokens },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Logout user (client-side token removal)
   */
  static async logout(req: Request, res: Response, next: NextFunction) {
    try {
      // In a JWT-based system, logout is primarily handled client-side
      // Here we can log the logout event and potentially blacklist the token

      if (req.user) {
        logger.info(`User logged out: ${(req.user as IUser).email}`);
      }

      res.json({
        success: true,
        message: "Logout successful",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Verify email
   */
  static async verifyEmail(req: Request, res: Response, next: NextFunction) {
    try {
      const { token } = req.query;

      if (!token || typeof token !== "string") {
        return next(createError("Verification token is required", 400));
      }

      await AuthService.verifyEmail(token);

      res.json({
        success: true,
        message: "Email verified successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Resend verification email
   */
  static async resendVerificationEmail(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { email } = req.body;

      if (!email) {
        return next(createError("Email is required", 400));
      }

      await AuthService.resendVerificationEmail(email);

      res.json({
        success: true,
        message: "Verification email sent successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { email } = req.body;

      if (!email) {
        return next(createError("Email is required", 400));
      }

      await AuthService.requestPasswordReset(email);

      res.json({
        success: true,
        message:
          "If an account with that email exists, a password reset link has been sent",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reset password
   */
  static async resetPassword(req: Request, res: Response, next: NextFunction) {
    try {
      const { token, password } = req.body;

      if (!token || !password) {
        return next(createError("Token and new password are required", 400));
      }

      await AuthService.resetPassword(token, password);

      // Send confirmation email (don't wait for it)
      try {
        const { email } = JWTService.verifyPasswordResetToken(token);
        const user = await import("@/models/User").then((m) =>
          m.User.findOne({ email })
        );
        if (user) {
          sendPasswordResetSuccessEmail(user.email, user.firstName).catch(
            (error) =>
              logger.error(
                "Failed to send password reset success email:",
                error
              )
          );
        }
      } catch (emailError) {
        logger.error("Error sending password reset success email:", emailError);
      }

      res.json({
        success: true,
        message: "Password reset successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Change password (authenticated user)
   */
  static async changePassword(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { currentPassword, newPassword } = req.body;

      if (!currentPassword || !newPassword) {
        return next(
          createError("Current password and new password are required", 400)
        );
      }

      await AuthService.changePassword(
        (req.user as IUser)._id.toString(),
        currentPassword,
        newPassword
      );

      res.json({
        success: true,
        message: "Password changed successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get current user profile
   */
  static async getProfile(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      // Remove sensitive fields
      const userProfile = (req.user as IUser).toObject();
      delete userProfile.password;
      delete userProfile.emailVerificationToken;
      delete userProfile.passwordResetToken;

      res.json({
        success: true,
        data: { user: userProfile },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Generate guest token
   */
  static async generateGuestToken(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const guestToken = AuthService.generateGuestToken();
      console.log(guestToken, "guestToken");
      res.json({
        success: true,
        message: "Guest token generated successfully",
        data: {
          token: guestToken,
          expiresIn: "24h",
        },
      });
    } catch (error) {
      console.log(error, "error");
      next(error);
    }
  }

  /**
   * Google OAuth initiation
   */
  static initiateGoogleAuth(req: Request, res: Response, next: NextFunction) {
    // This will be handled by passport middleware
    // The actual redirect happens in the route definition
  }

  /**
   * Google OAuth callback
   */
  static async googleAuthCallback(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return res.redirect(
          `${process.env.FRONTEND_URL}/login?error=oauth_failed`
        );
      }

      const user = req.user as any;
      const tokens = JWTService.generateTokenPair(user);

      // Redirect to frontend with tokens
      const redirectUrl = `${process.env.FRONTEND_URL}/auth/callback?token=${tokens.accessToken}&refresh=${tokens.refreshToken}`;
      res.redirect(redirectUrl);
    } catch (error) {
      logger.error("Google OAuth callback error:", error);
      res.redirect(`${process.env.FRONTEND_URL}/login?error=oauth_failed`);
    }
  }

  /**
   * Check authentication status
   */
  static async checkAuth(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.json({
          success: false,
          authenticated: false,
        });
      }

      const userProfile = (req.user as IUser).toObject();
      delete userProfile.password;
      delete userProfile.emailVerificationToken;
      delete userProfile.passwordResetToken;

      res.json({
        success: true,
        authenticated: true,
        data: { user: userProfile },
      });
    } catch (error) {
      next(error);
    }
  }
}
