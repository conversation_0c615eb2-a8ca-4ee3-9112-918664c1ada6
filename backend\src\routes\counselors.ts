import { Router } from "express";
import { Counselor<PERSON>ontroller } from "@/controllers/counselorController";
import { counselorValidations } from "@/utils/validation";
import {
  authenticate,
  requireCounselor,
  requireAdmin,
  optionalAuth,
} from "@/middleware/auth";
import {
  uploadVerificationDocuments,
  handleUploadError,
} from "@/middleware/upload";

const router = Router();

// Public routes
router.get("/", optionalAuth, CounselorController.getCounselors);

router.get("/:counselorId", CounselorController.getCounselorProfile);

router.get("/:counselorId/availability", CounselorController.getAvailableSlots);

router.get(
  "/:counselorId/weekly-availability",
  CounselorController.getWeeklyAvailability
);

// Counselor registration and profile management
router.post(
  "/register",
  authenticate,
  counselorValidations.register,
  CounselorController.registerCounselor
);

router.get(
  "/me/profile",
  authenticate,
  requireCounselor,
  CounselorController.getMyCounselorProfile
);

router.put(
  "/me/profile",
  authenticate,
  requireCounselor,
  CounselorController.updateCounselorProfile
);

router.put(
  "/me/availability",
  authenticate,
  requireCounselor,
  counselorValidations.updateAvailability,
  CounselorController.updateAvailability
);

router.post(
  "/me/verification-documents",
  authenticate,
  requireCounselor,
  uploadVerificationDocuments,
  handleUploadError,
  CounselorController.uploadVerificationDocuments
);

router.get(
  "/me/stats",
  authenticate,
  requireCounselor,
  CounselorController.getCounselorStats
);

router.put(
  "/me/settings",
  authenticate,
  requireCounselor,
  CounselorController.updateSettings
);

router.get(
  "/me/detailed-stats",
  authenticate,
  requireCounselor,
  CounselorController.getDetailedStats
);

router.post(
  "/me/block-slots",
  authenticate,
  requireCounselor,
  CounselorController.blockTimeSlots
);

router.post(
  "/me/unblock-slots",
  authenticate,
  requireCounselor,
  CounselorController.unblockTimeSlots
);

// Admin routes
router.get(
  "/admin/pending",
  authenticate,
  requireAdmin,
  CounselorController.getPendingApplications
);

router.put(
  "/:counselorId/approve",
  authenticate,
  requireAdmin,
  CounselorController.approveCounselor
);

router.put(
  "/:counselorId/reject",
  authenticate,
  requireAdmin,
  CounselorController.rejectCounselor
);

export default router;
