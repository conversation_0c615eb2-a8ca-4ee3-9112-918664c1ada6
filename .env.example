# Environment Configuration for Theramea Platform

# Application Environment
NODE_ENV=development
PORT=5000

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/theramea
MONGODB_TEST_URI=mongodb://localhost:27017/theramea_test

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=30d

# Email Configuration (for notifications and verification)
EMAIL_FROM=<EMAIL>
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Super Admin Configuration
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_USERNAME=superadmin
SUPER_ADMIN_PASSWORD=change-this-secure-password

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:5000/api/auth/google/callback

# Paystack Configuration (Payment Processing)
PAYSTACK_SECRET_KEY=sk_test_your-paystack-secret-key
PAYSTACK_PUBLIC_KEY=pk_test_your-paystack-public-key
PAYSTACK_WEBHOOK_SECRET=your-paystack-webhook-secret

# Daily.co Configuration (Video Sessions)
DAILY_API_KEY=your-daily-api-key
DAILY_DOMAIN=your-daily-domain.daily.co

# Cloudinary Configuration (File Uploads)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Security Configuration
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your-session-secret-key-change-this-in-production

# Platform Configuration
PLATFORM_FEE_PERCENTAGE=10
MAX_SESSION_DURATION=180
MIN_SESSION_DURATION=15
MAX_CHAT_PARTICIPANTS=50

# Development/Testing Configuration
ENABLE_LOGGING=true
LOG_LEVEL=info
