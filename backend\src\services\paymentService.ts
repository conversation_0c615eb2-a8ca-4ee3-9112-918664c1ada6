import axios from 'axios';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

export interface PaymentInitialization {
  amount: number;
  email: string;
  currency: 'NGN' | 'USD';
  reference: string;
  callback_url?: string;
  metadata?: any;
  channels?: string[];
}

export interface PaymentVerification {
  reference: string;
  amount: number;
  currency: string;
  status: string;
  gateway_response: string;
  paid_at: string;
  created_at: string;
  channel: string;
  fees: number;
  authorization: {
    authorization_code: string;
    bin: string;
    last4: string;
    exp_month: string;
    exp_year: string;
    channel: string;
    card_type: string;
    bank: string;
    country_code: string;
    brand: string;
    reusable: boolean;
    signature: string;
  };
  customer: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    customer_code: string;
    phone: string;
    metadata: any;
  };
}

export interface RefundRequest {
  transaction: string;
  amount?: number;
  currency?: string;
  customer_note?: string;
  merchant_note?: string;
}

export class PaymentService {
  private static readonly PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;
  private static readonly PAYSTACK_PUBLIC_KEY = process.env.PAYSTACK_PUBLIC_KEY;
  private static readonly PAYSTACK_BASE_URL = 'https://api.paystack.co';

  /**
   * Initialize payment transaction
   */
  static async initializePayment(data: PaymentInitialization): Promise<any> {
    try {
      if (!this.PAYSTACK_SECRET_KEY) {
        throw createError('Paystack secret key not configured', 500);
      }

      const response = await axios.post(
        `${this.PAYSTACK_BASE_URL}/transaction/initialize`,
        {
          amount: Math.round(data.amount * 100), // Convert to kobo/cents
          email: data.email,
          currency: data.currency,
          reference: data.reference,
          callback_url: data.callback_url,
          metadata: data.metadata,
          channels: data.channels || ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer']
        },
        {
          headers: {
            Authorization: `Bearer ${this.PAYSTACK_SECRET_KEY}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.status) {
        logger.info(`Payment initialized: ${data.reference} for ${data.amount} ${data.currency}`);
        return response.data.data;
      } else {
        throw createError(response.data.message || 'Payment initialization failed', 400);
      }
    } catch (error) {
      logger.error('Payment initialization error:', error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.message || 'Payment service error',
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Verify payment transaction
   */
  static async verifyPayment(reference: string): Promise<PaymentVerification> {
    try {
      if (!this.PAYSTACK_SECRET_KEY) {
        throw createError('Paystack secret key not configured', 500);
      }

      const response = await axios.get(
        `${this.PAYSTACK_BASE_URL}/transaction/verify/${reference}`,
        {
          headers: {
            Authorization: `Bearer ${this.PAYSTACK_SECRET_KEY}`
          }
        }
      );

      if (response.data.status) {
        logger.info(`Payment verified: ${reference}`);
        return response.data.data;
      } else {
        throw createError(response.data.message || 'Payment verification failed', 400);
      }
    } catch (error) {
      logger.error('Payment verification error:', error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.message || 'Payment verification failed',
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Create refund
   */
  static async createRefund(data: RefundRequest): Promise<any> {
    try {
      if (!this.PAYSTACK_SECRET_KEY) {
        throw createError('Paystack secret key not configured', 500);
      }

      const response = await axios.post(
        `${this.PAYSTACK_BASE_URL}/refund`,
        {
          transaction: data.transaction,
          amount: data.amount ? Math.round(data.amount * 100) : undefined,
          currency: data.currency,
          customer_note: data.customer_note,
          merchant_note: data.merchant_note
        },
        {
          headers: {
            Authorization: `Bearer ${this.PAYSTACK_SECRET_KEY}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.status) {
        logger.info(`Refund created: ${data.transaction}`);
        return response.data.data;
      } else {
        throw createError(response.data.message || 'Refund creation failed', 400);
      }
    } catch (error) {
      logger.error('Refund creation error:', error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.message || 'Refund service error',
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Get transaction details
   */
  static async getTransaction(transactionId: string): Promise<any> {
    try {
      if (!this.PAYSTACK_SECRET_KEY) {
        throw createError('Paystack secret key not configured', 500);
      }

      const response = await axios.get(
        `${this.PAYSTACK_BASE_URL}/transaction/${transactionId}`,
        {
          headers: {
            Authorization: `Bearer ${this.PAYSTACK_SECRET_KEY}`
          }
        }
      );

      if (response.data.status) {
        return response.data.data;
      } else {
        throw createError(response.data.message || 'Transaction not found', 404);
      }
    } catch (error) {
      logger.error('Get transaction error:', error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.message || 'Transaction service error',
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Create customer
   */
  static async createCustomer(email: string, firstName: string, lastName: string, phone?: string): Promise<any> {
    try {
      if (!this.PAYSTACK_SECRET_KEY) {
        throw createError('Paystack secret key not configured', 500);
      }

      const response = await axios.post(
        `${this.PAYSTACK_BASE_URL}/customer`,
        {
          email,
          first_name: firstName,
          last_name: lastName,
          phone
        },
        {
          headers: {
            Authorization: `Bearer ${this.PAYSTACK_SECRET_KEY}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.status) {
        logger.info(`Customer created: ${email}`);
        return response.data.data;
      } else {
        throw createError(response.data.message || 'Customer creation failed', 400);
      }
    } catch (error) {
      logger.error('Customer creation error:', error);
      if (axios.isAxiosError(error)) {
        // Customer might already exist
        if (error.response?.status === 400) {
          return await this.getCustomer(email);
        }
        throw createError(
          error.response?.data?.message || 'Customer service error',
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Get customer by email
   */
  static async getCustomer(email: string): Promise<any> {
    try {
      if (!this.PAYSTACK_SECRET_KEY) {
        throw createError('Paystack secret key not configured', 500);
      }

      const response = await axios.get(
        `${this.PAYSTACK_BASE_URL}/customer/${email}`,
        {
          headers: {
            Authorization: `Bearer ${this.PAYSTACK_SECRET_KEY}`
          }
        }
      );

      if (response.data.status) {
        return response.data.data;
      } else {
        throw createError(response.data.message || 'Customer not found', 404);
      }
    } catch (error) {
      logger.error('Get customer error:', error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.message || 'Customer service error',
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Generate payment reference
   */
  static generatePaymentReference(prefix: string = 'TH'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Calculate platform fee
   */
  static calculatePlatformFee(amount: number, feePercentage: number = 10): number {
    return Math.round(amount * (feePercentage / 100) * 100) / 100;
  }

  /**
   * Calculate counselor earnings
   */
  static calculateCounselorEarnings(amount: number, feePercentage: number = 10): number {
    const platformFee = this.calculatePlatformFee(amount, feePercentage);
    return Math.round((amount - platformFee) * 100) / 100;
  }

  /**
   * Validate webhook signature
   */
  static validateWebhookSignature(payload: string, signature: string): boolean {
    try {
      const crypto = require('crypto');
      const hash = crypto
        .createHmac('sha512', this.PAYSTACK_SECRET_KEY)
        .update(payload, 'utf-8')
        .digest('hex');
      
      return hash === signature;
    } catch (error) {
      logger.error('Webhook signature validation error:', error);
      return false;
    }
  }

  /**
   * Get supported banks
   */
  static async getSupportedBanks(country: string = 'nigeria'): Promise<any[]> {
    try {
      if (!this.PAYSTACK_SECRET_KEY) {
        throw createError('Paystack secret key not configured', 500);
      }

      const response = await axios.get(
        `${this.PAYSTACK_BASE_URL}/bank?country=${country}`,
        {
          headers: {
            Authorization: `Bearer ${this.PAYSTACK_SECRET_KEY}`
          }
        }
      );

      if (response.data.status) {
        return response.data.data;
      } else {
        throw createError(response.data.message || 'Failed to get banks', 400);
      }
    } catch (error) {
      logger.error('Get supported banks error:', error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.message || 'Bank service error',
          error.response?.status || 500
        );
      }
      throw error;
    }
  }
}
