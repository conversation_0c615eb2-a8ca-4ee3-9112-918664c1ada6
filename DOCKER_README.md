# 🐳 Theramea Docker Setup

Run the complete Theramea platform with Docker Compose in just a few commands!

## 📋 Prerequisites

### 1. Install Docker Desktop
- **Windows/Mac**: Download from [Docker Desktop](https://www.docker.com/products/docker-desktop)
- **Linux**: Install Docker Engine and Docker Compose

### 2. Verify Installation
```bash
docker --version
docker-compose --version
```

## 🚀 Quick Start

### Windows Users
```cmd
# Start the application
docker-setup.bat start

# View logs
docker-setup.bat logs

# Stop the application
docker-setup.bat stop
```

### Linux/Mac Users
```bash
# Make script executable
chmod +x docker-setup.sh

# Start the application
./docker-setup.sh start

# View logs
./docker-setup.sh logs

# Stop the application
./docker-setup.sh stop
```

## 🏗️ What Gets Started

The Docker Compose setup includes:

### Core Services
- **MongoDB** (Port 27017) - Database
- **Redis** (Port 6379) - Cache & Sessions
- **Backend API** (Port 5000) - Node.js/Express API
- **Frontend** (Port 3000) - Next.js Application
- **Nginx** (Port 80) - Reverse Proxy (Optional)

### Service Architecture
```
┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │
│   (Next.js)     │◄──►│   (Express)     │
│   Port: 3000    │    │   Port: 5000    │
└─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │    MongoDB      │
│  (Reverse Proxy)│    │   (Database)    │
│   Port: 80      │    │   Port: 27017   │
└─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │     Redis       │
                       │    (Cache)      │
                       │   Port: 6379    │
                       └─────────────────┘
```

## 🌐 Access URLs

Once started, access the application at:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000/api
- **Health Check**: http://localhost:5000/api/health
- **Testing Page**: http://localhost:3000/testing
- **Nginx Proxy**: http://localhost:80 (if enabled)

## 👥 Test Accounts

The setup automatically creates test accounts:

| Role | Email | Password |
|------|-------|----------|
| User | <EMAIL> | password123 |
| Counselor | <EMAIL> | password123 |
| Admin | <EMAIL> | password123 |

## 📊 Database Access

### MongoDB
```bash
# Connect to MongoDB
docker exec -it theramea-mongodb mongosh

# Or use connection string
****************************************************
```

### Redis
```bash
# Connect to Redis
docker exec -it theramea-redis redis-cli

# Or use connection string
redis://localhost:6379
```

## 🛠️ Available Commands

### Windows (docker-setup.bat)
```cmd
docker-setup.bat start     # Start all services
docker-setup.bat stop      # Stop all services
docker-setup.bat restart   # Restart all services
docker-setup.bat logs      # Show recent logs
docker-setup.bat status    # Show service status
docker-setup.bat clean     # Clean up all resources
docker-setup.bat rebuild   # Rebuild and restart
```

### Linux/Mac (docker-setup.sh)
```bash
./docker-setup.sh start     # Start all services
./docker-setup.sh stop      # Stop all services
./docker-setup.sh restart   # Restart all services
./docker-setup.sh logs      # Show recent logs
./docker-setup.sh status    # Show service status
./docker-setup.sh clean     # Clean up all resources
./docker-setup.sh rebuild   # Rebuild and restart
```

## 🔧 Manual Docker Commands

If you prefer manual control:

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down

# Rebuild and start
docker-compose up --build -d

# Remove everything including volumes
docker-compose down --volumes --remove-orphans
```

## 📝 Environment Configuration

### Backend Environment Variables
The Docker setup uses these environment variables:

```env
NODE_ENV=development
MONGODB_URI=*******************************************************************
REDIS_URL=redis://redis:6379
JWT_SECRET=theramea-super-secret-jwt-key-development-only
PAYSTACK_SECRET_KEY=sk_test_your-paystack-secret-key
DAILY_API_KEY=your-daily-api-key
```

### Frontend Environment Variables
```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_SOCKET_URL=http://localhost:5000
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_your-paystack-public-key
```

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Check what's using the ports
netstat -tulpn | grep :3000
netstat -tulpn | grep :5000

# Kill processes if needed
sudo kill -9 <PID>
```

#### Docker Not Running
- Start Docker Desktop
- Wait for Docker to fully initialize
- Check Docker status: `docker info`

#### Services Not Starting
```bash
# Check logs for specific service
docker-compose logs backend
docker-compose logs frontend
docker-compose logs mongodb

# Restart specific service
docker-compose restart backend
```

#### Database Connection Issues
```bash
# Check MongoDB status
docker exec theramea-mongodb mongosh --eval "db.runCommand('ping')"

# Check Redis status
docker exec theramea-redis redis-cli ping
```

#### Permission Issues (Linux/Mac)
```bash
# Fix script permissions
chmod +x docker-setup.sh

# Fix Docker permissions
sudo usermod -aG docker $USER
# Then logout and login again
```

### Clean Restart
If you encounter persistent issues:

```bash
# Complete cleanup
./docker-setup.sh clean

# Or manually
docker-compose down --volumes --remove-orphans
docker system prune -f
docker volume prune -f

# Then restart
./docker-setup.sh start
```

## 📊 Monitoring

### View Service Status
```bash
docker-compose ps
```

### View Resource Usage
```bash
docker stats
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
```

## 🔒 Security Notes

- Default passwords are for development only
- Change all default credentials in production
- MongoDB and Redis are not exposed externally in production
- Use environment-specific configuration files

## 🚀 Production Deployment

For production deployment:

1. Update environment variables
2. Use production-ready images
3. Set up proper SSL certificates
4. Configure external databases
5. Set up monitoring and logging
6. Use Docker Swarm or Kubernetes

## 📞 Support

If you encounter issues:

1. Check the logs: `docker-compose logs`
2. Verify all services are running: `docker-compose ps`
3. Check the troubleshooting section above
4. Restart services: `./docker-setup.sh restart`

Happy coding! 🎉
