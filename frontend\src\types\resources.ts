// Resource types for Self-Help Library

export interface Resource {
  _id: string;
  title: string;
  description: string;
  content: string;
  type: 'article' | 'video' | 'audio' | 'tool' | 'worksheet' | 'guide';
  category: string;
  subcategory?: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadTime?: number; // in minutes
  estimatedDuration?: number; // in minutes for videos/audio
  media: {
    thumbnailUrl?: string;
    videoUrl?: string;
    audioUrl?: string;
    downloadUrl?: string;
    fileSize?: number;
  };
  author: {
    name: string;
    credentials?: string;
    bio?: string;
    profilePicture?: string;
  };
  seo: {
    metaTitle?: string;
    metaDescription?: string;
    keywords: string[];
    slug: string;
  };
  statistics: {
    views: number;
    likes: number;
    bookmarks: number;
    shares: number;
    averageRating: number;
    totalRatings: number;
    completionRate: number;
  };
  interactions: {
    userId: string;
    action: 'view' | 'like' | 'bookmark' | 'share' | 'complete';
    timestamp: string;
  }[];
  ratings: {
    userId: string;
    rating: number; // 1-5
    review?: string;
    createdAt: string;
  }[];
  bookmarkedBy: string[];
  relatedResources: string[];
  prerequisites: string[];
  learningObjectives: string[];
  isPublished: boolean;
  isFeatured: boolean;
  isPremium: boolean;
  publishedAt?: string;
  lastUpdatedAt: string;
  createdBy: string;
  reviewedBy?: string;
  reviewedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ResourceFilters {
  type?: string;
  category?: string;
  subcategory?: string;
  tags?: string[];
  difficulty?: string;
  isPremium?: boolean;
  search?: string;
  minRating?: number;
}

export interface ResourceListOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ResourceCategory {
  value: string;
  label: string;
  description: string;
  icon: string;
  color: string;
  subcategories?: {
    value: string;
    label: string;
  }[];
}

// Resource categories with display information
export const RESOURCE_CATEGORIES: ResourceCategory[] = [
  {
    value: 'mental-health-basics',
    label: 'Mental Health Basics',
    description: 'Foundational knowledge about mental health and wellness',
    icon: '🧠',
    color: 'blue',
    subcategories: [
      { value: 'understanding-mental-health', label: 'Understanding Mental Health' },
      { value: 'common-conditions', label: 'Common Conditions' },
      { value: 'myths-facts', label: 'Myths & Facts' },
    ],
  },
  {
    value: 'anxiety-management',
    label: 'Anxiety Management',
    description: 'Tools and techniques for managing anxiety',
    icon: '😰',
    color: 'yellow',
    subcategories: [
      { value: 'breathing-techniques', label: 'Breathing Techniques' },
      { value: 'cognitive-strategies', label: 'Cognitive Strategies' },
      { value: 'exposure-therapy', label: 'Exposure Therapy' },
    ],
  },
  {
    value: 'depression-support',
    label: 'Depression Support',
    description: 'Resources for understanding and managing depression',
    icon: '💙',
    color: 'indigo',
    subcategories: [
      { value: 'mood-tracking', label: 'Mood Tracking' },
      { value: 'behavioral-activation', label: 'Behavioral Activation' },
      { value: 'support-systems', label: 'Support Systems' },
    ],
  },
  {
    value: 'stress-relief',
    label: 'Stress Relief',
    description: 'Effective strategies for stress management',
    icon: '😌',
    color: 'green',
    subcategories: [
      { value: 'relaxation-techniques', label: 'Relaxation Techniques' },
      { value: 'time-management', label: 'Time Management' },
      { value: 'work-life-balance', label: 'Work-Life Balance' },
    ],
  },
  {
    value: 'relationship-skills',
    label: 'Relationship Skills',
    description: 'Building healthy relationships and communication',
    icon: '💕',
    color: 'pink',
    subcategories: [
      { value: 'communication', label: 'Communication' },
      { value: 'conflict-resolution', label: 'Conflict Resolution' },
      { value: 'boundaries', label: 'Setting Boundaries' },
    ],
  },
  {
    value: 'self-care',
    label: 'Self-Care',
    description: 'Practices for physical and emotional well-being',
    icon: '🌸',
    color: 'purple',
    subcategories: [
      { value: 'daily-routines', label: 'Daily Routines' },
      { value: 'physical-wellness', label: 'Physical Wellness' },
      { value: 'emotional-wellness', label: 'Emotional Wellness' },
    ],
  },
  {
    value: 'mindfulness-meditation',
    label: 'Mindfulness & Meditation',
    description: 'Mindfulness practices and meditation techniques',
    icon: '🧘',
    color: 'teal',
    subcategories: [
      { value: 'guided-meditations', label: 'Guided Meditations' },
      { value: 'mindful-living', label: 'Mindful Living' },
      { value: 'body-awareness', label: 'Body Awareness' },
    ],
  },
  {
    value: 'coping-strategies',
    label: 'Coping Strategies',
    description: 'Healthy ways to cope with life challenges',
    icon: '💪',
    color: 'orange',
    subcategories: [
      { value: 'problem-solving', label: 'Problem Solving' },
      { value: 'emotional-regulation', label: 'Emotional Regulation' },
      { value: 'resilience-building', label: 'Resilience Building' },
    ],
  },
  {
    value: 'crisis-resources',
    label: 'Crisis Resources',
    description: 'Immediate help and crisis intervention resources',
    icon: '🆘',
    color: 'red',
    subcategories: [
      { value: 'emergency-contacts', label: 'Emergency Contacts' },
      { value: 'safety-planning', label: 'Safety Planning' },
      { value: 'immediate-support', label: 'Immediate Support' },
    ],
  },
  {
    value: 'workplace-wellness',
    label: 'Workplace Wellness',
    description: 'Mental health in professional environments',
    icon: '💼',
    color: 'gray',
    subcategories: [
      { value: 'workplace-stress', label: 'Workplace Stress' },
      { value: 'career-development', label: 'Career Development' },
      { value: 'work-relationships', label: 'Work Relationships' },
    ],
  },
];

export const RESOURCE_TYPES = [
  { value: 'article', label: 'Articles', icon: '📄', description: 'Written content and guides' },
  { value: 'video', label: 'Videos', icon: '🎥', description: 'Video content and tutorials' },
  { value: 'audio', label: 'Audio', icon: '🎧', description: 'Podcasts and audio guides' },
  { value: 'tool', label: 'Tools', icon: '🛠️', description: 'Interactive tools and assessments' },
  { value: 'worksheet', label: 'Worksheets', icon: '📝', description: 'Downloadable worksheets and exercises' },
  { value: 'guide', label: 'Guides', icon: '📚', description: 'Step-by-step guides and manuals' },
] as const;

export const DIFFICULTY_LEVELS = [
  { value: 'beginner', label: 'Beginner', color: 'green', description: 'Easy to understand and follow' },
  { value: 'intermediate', label: 'Intermediate', color: 'yellow', description: 'Some prior knowledge helpful' },
  { value: 'advanced', label: 'Advanced', color: 'red', description: 'Requires deeper understanding' },
] as const;

export type ResourceType = typeof RESOURCE_TYPES[number]['value'];
export type DifficultyLevel = typeof DIFFICULTY_LEVELS[number]['value'];
