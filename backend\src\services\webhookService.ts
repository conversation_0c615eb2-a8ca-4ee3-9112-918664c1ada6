import { Session } from '@/models/Session';
import { PaymentService } from '@/services/paymentService';
import { BookingService } from '@/services/bookingService';
import { logger } from '@/utils/logger';
import { sendEmail } from '@/utils/email';

export interface WebhookEvent {
  event: string;
  data: any;
}

export class WebhookService {
  /**
   * Process Paystack webhook events
   */
  static async processPaystackWebhook(event: WebhookEvent): Promise<void> {
    try {
      switch (event.event) {
        case 'charge.success':
          await this.handleChargeSuccess(event.data);
          break;

        case 'charge.failed':
          await this.handleChargeFailed(event.data);
          break;

        case 'transfer.success':
          await this.handleTransferSuccess(event.data);
          break;

        case 'transfer.failed':
          await this.handleTransferFailed(event.data);
          break;

        case 'refund.processed':
          await this.handleRefundProcessed(event.data);
          break;

        case 'subscription.create':
          await this.handleSubscriptionCreate(event.data);
          break;

        case 'subscription.disable':
          await this.handleSubscriptionDisable(event.data);
          break;

        default:
          logger.info(`Unhandled Paystack webhook event: ${event.event}`);
      }
    } catch (error) {
      logger.error('Process Paystack webhook error:', error);
      throw error;
    }
  }

  /**
   * Handle successful charge
   */
  private static async handleChargeSuccess(data: any): Promise<void> {
    try {
      const { reference, amount, customer, metadata } = data;

      logger.info(`Processing successful charge: ${reference}`);

      // Find session by payment reference
      const session = await Session.findOne({ 'payment.reference': reference })
        .populate('clientId')
        .populate('counselorId');

      if (!session) {
        logger.warn(`Session not found for payment reference: ${reference}`);
        return;
      }

      // Update session payment status
      session.payment.status = 'completed';
      session.payment.paidAt = new Date(data.paid_at);
      session.payment.transactionId = data.id;
      session.payment.gateway_response = data.gateway_response;
      session.payment.fees = data.fees / 100; // Convert from kobo/cents

      // Update session status
      if (session.status === 'pending_payment') {
        session.status = 'scheduled';
      }

      await session.save();

      // Update counselor earnings
      const counselorEarnings = session.pricing.totalAmount - session.pricing.platformFee;
      await this.updateCounselorEarnings(session.counselorId.toString(), counselorEarnings);

      // Send confirmation notifications
      await this.sendPaymentSuccessNotifications(session);

      logger.info(`Payment processed successfully: ${reference}`);
    } catch (error) {
      logger.error('Handle charge success error:', error);
    }
  }

  /**
   * Handle failed charge
   */
  private static async handleChargeFailed(data: any): Promise<void> {
    try {
      const { reference, gateway_response } = data;

      logger.info(`Processing failed charge: ${reference}`);

      // Find session by payment reference
      const session = await Session.findOne({ 'payment.reference': reference })
        .populate('clientId');

      if (!session) {
        logger.warn(`Session not found for payment reference: ${reference}`);
        return;
      }

      // Update session payment status
      session.payment.status = 'failed';
      session.payment.gateway_response = gateway_response;
      session.status = 'cancelled';

      await session.save();

      // Send failure notification
      await this.sendPaymentFailureNotification(session, gateway_response);

      logger.info(`Payment failure processed: ${reference}`);
    } catch (error) {
      logger.error('Handle charge failed error:', error);
    }
  }

  /**
   * Handle successful transfer (counselor payout)
   */
  private static async handleTransferSuccess(data: any): Promise<void> {
    try {
      const { reference, amount, recipient } = data;

      logger.info(`Processing successful transfer: ${reference}`);

      // TODO: Update counselor payout records when implemented
      // This would track successful payouts to counselors

      logger.info(`Transfer processed successfully: ${reference}`);
    } catch (error) {
      logger.error('Handle transfer success error:', error);
    }
  }

  /**
   * Handle failed transfer
   */
  private static async handleTransferFailed(data: any): Promise<void> {
    try {
      const { reference, failure_reason } = data;

      logger.info(`Processing failed transfer: ${reference}`);

      // TODO: Handle failed counselor payouts
      // This would retry or notify about failed payouts

      logger.info(`Transfer failure processed: ${reference}`);
    } catch (error) {
      logger.error('Handle transfer failed error:', error);
    }
  }

  /**
   * Handle processed refund
   */
  private static async handleRefundProcessed(data: any): Promise<void> {
    try {
      const { transaction, amount, status } = data;

      logger.info(`Processing refund: ${transaction}`);

      // Find session by transaction ID
      const session = await Session.findOne({ 'payment.transactionId': transaction })
        .populate('clientId');

      if (!session) {
        logger.warn(`Session not found for transaction: ${transaction}`);
        return;
      }

      // Update session refund status
      if (!session.refund) {
        session.refund = {
          amount: amount / 100, // Convert from kobo/cents
          status: status,
          processedAt: new Date(),
          reference: data.id
        };
      } else {
        session.refund.status = status;
        session.refund.processedAt = new Date();
      }

      await session.save();

      // Send refund confirmation
      await this.sendRefundConfirmationNotification(session);

      logger.info(`Refund processed: ${transaction}`);
    } catch (error) {
      logger.error('Handle refund processed error:', error);
    }
  }

  /**
   * Handle subscription creation (for recurring sessions)
   */
  private static async handleSubscriptionCreate(data: any): Promise<void> {
    try {
      const { subscription_code, customer, plan } = data;

      logger.info(`Processing subscription creation: ${subscription_code}`);

      // TODO: Handle subscription-based sessions when implemented
      // This would be for recurring therapy sessions

      logger.info(`Subscription created: ${subscription_code}`);
    } catch (error) {
      logger.error('Handle subscription create error:', error);
    }
  }

  /**
   * Handle subscription disable
   */
  private static async handleSubscriptionDisable(data: any): Promise<void> {
    try {
      const { subscription_code } = data;

      logger.info(`Processing subscription disable: ${subscription_code}`);

      // TODO: Handle subscription cancellation when implemented

      logger.info(`Subscription disabled: ${subscription_code}`);
    } catch (error) {
      logger.error('Handle subscription disable error:', error);
    }
  }

  /**
   * Update counselor earnings
   */
  private static async updateCounselorEarnings(counselorId: string, earnings: number): Promise<void> {
    try {
      const { Counselor } = await import('@/models/Counselor');
      
      await Counselor.findByIdAndUpdate(counselorId, {
        $inc: {
          'statistics.totalEarnings': earnings,
          'statistics.totalSessions': 1
        }
      });

      logger.info(`Counselor earnings updated: ${counselorId} - ${earnings}`);
    } catch (error) {
      logger.error('Update counselor earnings error:', error);
    }
  }

  /**
   * Send payment success notifications
   */
  private static async sendPaymentSuccessNotifications(session: any): Promise<void> {
    try {
      const client = session.clientId;
      const counselor = session.counselorId;

      // Send confirmation to client
      await sendEmail({
        to: client.email,
        subject: 'Payment Confirmed - Session Scheduled',
        html: `
          <h2>Payment Confirmed</h2>
          <p>Hi ${client.firstName},</p>
          <p>Your payment has been confirmed and your session is now scheduled!</p>
          <p><strong>Session Details:</strong></p>
          <ul>
            <li>Counselor: ${counselor.userId.firstName} ${counselor.userId.lastName}</li>
            <li>Date & Time: ${session.scheduledAt.toLocaleString()}</li>
            <li>Duration: ${session.duration} minutes</li>
            <li>Amount Paid: ${session.pricing.currency} ${session.pricing.totalAmount}</li>
          </ul>
          <p>You will receive a session link closer to the appointment time.</p>
          <p>Best regards,<br>The Theramea Team</p>
        `
      });

      // Notify counselor
      await sendEmail({
        to: counselor.userId.email,
        subject: 'Session Payment Confirmed - Theramea',
        html: `
          <h2>Session Payment Confirmed</h2>
          <p>Hi ${counselor.userId.firstName},</p>
          <p>Payment has been confirmed for your upcoming session!</p>
          <p><strong>Session Details:</strong></p>
          <ul>
            <li>Client: ${client.firstName} ${client.lastName}</li>
            <li>Date & Time: ${session.scheduledAt.toLocaleString()}</li>
            <li>Duration: ${session.duration} minutes</li>
            <li>Your Earnings: ${session.pricing.currency} ${session.pricing.totalAmount - session.pricing.platformFee}</li>
          </ul>
          <p>Please prepare for the session and log in to your dashboard for session details.</p>
          <p>Best regards,<br>The Theramea Team</p>
        `
      });
    } catch (error) {
      logger.error('Send payment success notifications error:', error);
    }
  }

  /**
   * Send payment failure notification
   */
  private static async sendPaymentFailureNotification(session: any, reason: string): Promise<void> {
    try {
      const client = session.clientId;

      await sendEmail({
        to: client.email,
        subject: 'Payment Failed - Session Cancelled',
        html: `
          <h2>Payment Failed</h2>
          <p>Hi ${client.firstName},</p>
          <p>Unfortunately, your payment could not be processed and your session has been cancelled.</p>
          <p><strong>Reason:</strong> ${reason}</p>
          <p>Please try booking again or contact support if you continue to experience issues.</p>
          <p>Best regards,<br>The Theramea Team</p>
        `
      });
    } catch (error) {
      logger.error('Send payment failure notification error:', error);
    }
  }

  /**
   * Send refund confirmation notification
   */
  private static async sendRefundConfirmationNotification(session: any): Promise<void> {
    try {
      const client = session.clientId;

      await sendEmail({
        to: client.email,
        subject: 'Refund Processed - Theramea',
        html: `
          <h2>Refund Processed</h2>
          <p>Hi ${client.firstName},</p>
          <p>Your refund has been processed successfully.</p>
          <p><strong>Refund Details:</strong></p>
          <ul>
            <li>Amount: ${session.pricing.currency} ${session.refund.amount}</li>
            <li>Status: ${session.refund.status}</li>
            <li>Processed: ${session.refund.processedAt.toLocaleString()}</li>
          </ul>
          <p>The refund should appear in your account within 3-5 business days.</p>
          <p>Best regards,<br>The Theramea Team</p>
        `
      });
    } catch (error) {
      logger.error('Send refund confirmation notification error:', error);
    }
  }
}
