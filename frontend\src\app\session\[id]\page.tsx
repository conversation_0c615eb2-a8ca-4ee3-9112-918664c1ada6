'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import { videoAPI } from '@/lib/video';
import { bookingAPI } from '@/lib/counselor';
import { Session } from '@/types/counselor';
import { MeetingToken } from '@/types/video';
import VideoCall from '@/components/video/VideoCall';
import Header from '@/components/layout/Header';

export default function VideoSessionPage() {
  const router = useRouter();
  const params = useParams();
  const sessionId = params.id as string;
  
  const { isAuthenticated, user, checkAuth, isLoading, tokens } = useAuthStore();
  const [session, setSession] = useState<Session | null>(null);
  const [meetingToken, setMeetingToken] = useState<MeetingToken | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionStarted, setSessionStarted] = useState(false);

  const token = tokens?.accessToken;

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      router.push('/auth/login');
      return;
    }

    if (isAuthenticated && token && sessionId) {
      fetchSessionData();
    }
  }, [isAuthenticated, token, sessionId, isLoading, router]);

  const fetchSessionData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get session details
      const sessionResponse = await bookingAPI.getBooking(sessionId, token!);
      const sessionData = sessionResponse.data.session;
      setSession(sessionData);

      // Verify user has access to this session
      const isClient = sessionData.userId === user?._id;
      const isCounselor = sessionData.counselorId === user?._id;

      if (!isClient && !isCounselor) {
        setError('You do not have access to this session');
        return;
      }

      // Check if session is scheduled for now (within 15 minutes)
      const sessionTime = new Date(sessionData.scheduledAt);
      const now = new Date();
      const timeDiff = sessionTime.getTime() - now.getTime();
      const minutesDiff = Math.floor(timeDiff / (1000 * 60));

      if (minutesDiff > 15) {
        setError(`Session starts in ${minutesDiff} minutes. Please return at the scheduled time.`);
        return;
      }

      if (minutesDiff < -60) {
        setError('This session has expired.');
        return;
      }

      // Create room if it doesn't exist
      if (!sessionData.videoSession?.roomUrl) {
        await videoAPI.createRoom(sessionId, token!);
      }

      // Generate meeting token
      const tokenResponse = await videoAPI.generateToken(sessionId, token!);
      setMeetingToken(tokenResponse.data.token);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load session');
    } finally {
      setLoading(false);
    }
  };

  const handleStartSession = async () => {
    if (!token) return;

    try {
      await videoAPI.startSession(sessionId, token);
      setSessionStarted(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start session');
    }
  };

  const handleCallEnd = () => {
    router.push('/dashboard');
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const getUserRole = (): 'client' | 'counselor' => {
    if (!session || !user) return 'client';
    return session.counselorId === user._id ? 'counselor' : 'client';
  };

  const getUserName = (): string => {
    if (!user) return 'User';
    return `${user.firstName} ${user.lastName}`;
  };

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <svg className="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Session Error</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => router.push('/dashboard')}
              className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700"
            >
              Return to Dashboard
            </button>
          </div>
        </main>
      </div>
    );
  }

  if (!session || !meetingToken) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading session...</p>
        </div>
      </div>
    );
  }

  // Show pre-session screen
  if (!sessionStarted) {
    const userRole = getUserRole();
    const isClient = userRole === 'client';
    const sessionTime = new Date(session.scheduledAt);
    const now = new Date();
    const canStart = (sessionTime.getTime() - now.getTime()) <= 15 * 60 * 1000; // 15 minutes before

    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {isClient ? 'Your Counseling Session' : 'Client Session'}
              </h1>
              <p className="text-gray-600">
                Scheduled for {sessionTime.toLocaleDateString()} at{' '}
                {sessionTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </p>
            </div>

            {/* Session Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Session Details</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Duration:</span>
                    <span className="font-medium">{session.duration} minutes</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Type:</span>
                    <span className="font-medium capitalize">{session.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className="font-medium capitalize">{session.status}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="font-semibold text-gray-900 mb-4">
                  {isClient ? 'Your Counselor' : 'Client Information'}
                </h3>
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-lg font-semibold text-purple-600">
                      {isClient 
                        ? session.counselor?.user?.firstName?.charAt(0) || 'C'
                        : session.user?.firstName?.charAt(0) || 'U'
                      }
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {isClient 
                        ? `${session.counselor?.user?.firstName} ${session.counselor?.user?.lastName}`
                        : `${session.user?.firstName} ${session.user?.lastName}`
                      }
                    </p>
                    <p className="text-sm text-gray-600">
                      {isClient ? 'Licensed Counselor' : 'Client'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Pre-session checklist */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h3 className="font-semibold text-blue-900 mb-4">Before you start:</h3>
              <div className="space-y-2 text-blue-800">
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm">Ensure you have a stable internet connection</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm">Find a quiet, private space</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm">Test your camera and microphone</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm">Close other applications to improve performance</span>
                </div>
              </div>
            </div>

            {/* Start Session Button */}
            <div className="text-center">
              {canStart ? (
                <button
                  onClick={handleStartSession}
                  className="bg-purple-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors"
                >
                  Start Session
                </button>
              ) : (
                <div>
                  <p className="text-gray-600 mb-4">
                    Session will be available 15 minutes before the scheduled time
                  </p>
                  <button
                    disabled
                    className="bg-gray-300 text-gray-500 px-8 py-3 rounded-lg font-medium cursor-not-allowed"
                  >
                    Session Not Ready
                  </button>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    );
  }

  // Show video call interface
  return (
    <VideoCall
      sessionId={sessionId}
      token={meetingToken.token}
      roomUrl={session.videoSession?.roomUrl || ''}
      userRole={getUserRole()}
      userName={getUserName()}
      onCallEnd={handleCallEnd}
      onError={handleError}
    />
  );
}
