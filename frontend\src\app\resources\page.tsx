'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuthStore } from '@/store/authStore';
import { resourcesAPI } from '@/lib/resources';
import { Resource, RESOURCE_CATEGORIES } from '@/types/resources';
import Header from '@/components/layout/Header';
import ResourceList from '@/components/resources/ResourceList';
import ResourceCard from '@/components/resources/ResourceCard';

export default function ResourcesPage() {
  const router = useRouter();
  const { isAuthenticated, isGuest, checkAuth, isLoading, tokens, guestToken } = useAuthStore();
  const [featuredResources, setFeaturedResources] = useState<Resource[]>([]);
  const [trendingResources, setTrendingResources] = useState<Resource[]>([]);
  const [featuredLoading, setFeaturedLoading] = useState(true);

  const token = tokens?.accessToken || guestToken;

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isGuest && !isLoading) {
      router.push('/');
    }
  }, [isAuthenticated, isGuest, isLoading, router]);

  useEffect(() => {
    if (token) {
      fetchFeaturedContent();
    }
  }, [token]);

  const fetchFeaturedContent = async () => {
    try {
      setFeaturedLoading(true);
      
      const [featuredResponse, trendingResponse] = await Promise.all([
        resourcesAPI.getFeaturedResources(6, token),
        resourcesAPI.getTrendingResources(8, token),
      ]);
      
      setFeaturedResources(featuredResponse.data.content);
      setTrendingResources(trendingResponse.data.content);
    } catch (error) {
      console.error('Failed to fetch featured content:', error);
    } finally {
      setFeaturedLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!isAuthenticated && !isGuest) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Self-Help Library</h1>
          <p className="text-gray-600">
            Discover articles, videos, tools, and guides to support your mental health journey.
          </p>
        </div>

        {/* Guest Notice */}
        {isGuest && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-blue-900 mb-2">
                  Browsing as Guest
                </h3>
                <p className="text-blue-800 mb-4">
                  You can access free resources as a guest. Create an account to bookmark resources, track your progress, get personalized recommendations, and access premium content.
                </p>
                <div className="flex flex-col sm:flex-row gap-3">
                  <Link
                    href="/auth/register"
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-center font-medium"
                  >
                    Create Free Account
                  </Link>
                  <Link
                    href="/auth/login"
                    className="border border-blue-300 text-blue-700 px-4 py-2 rounded-md hover:bg-blue-50 transition-colors text-center font-medium"
                  >
                    Sign In
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Categories Overview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Browse by Category</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {RESOURCE_CATEGORIES.slice(0, 10).map((category) => (
              <Link
                key={category.value}
                href={`/resources?category=${category.value}`}
                className="group p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all text-center"
              >
                <div className="text-2xl mb-2">{category.icon}</div>
                <h3 className="text-sm font-medium text-gray-900 group-hover:text-purple-600 transition-colors">
                  {category.label}
                </h3>
                <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                  {category.description}
                </p>
              </Link>
            ))}
          </div>
        </div>

        {/* Featured Resources */}
        {featuredResources.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold text-gray-900">Featured Resources</h2>
              <Link
                href="/resources?featured=true"
                className="text-purple-600 hover:text-purple-700 font-medium"
              >
                View all →
              </Link>
            </div>
            
            {featuredLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 animate-pulse">
                    <div className="h-48 bg-gray-200 rounded-t-lg"></div>
                    <div className="p-4 space-y-3">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-full"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredResources.map((resource) => (
                  <ResourceCard key={resource._id} resource={resource} />
                ))}
              </div>
            )}
          </div>
        )}

        {/* Trending Resources */}
        {trendingResources.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold text-gray-900">Trending Now</h2>
              <Link
                href="/resources?trending=true"
                className="text-purple-600 hover:text-purple-700 font-medium"
              >
                View all →
              </Link>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {trendingResources.slice(0, 4).map((resource) => (
                <ResourceCard key={resource._id} resource={resource} />
              ))}
            </div>
          </div>
        )}

        {/* Quick Access Tools */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Access Tools</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link
              href="/resources?type=tool"
              className="group p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all"
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">🛠️</div>
                <div>
                  <h3 className="font-medium text-gray-900 group-hover:text-purple-600">
                    Interactive Tools
                  </h3>
                  <p className="text-sm text-gray-500">Assessments & exercises</p>
                </div>
              </div>
            </Link>

            <Link
              href="/resources?type=worksheet"
              className="group p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all"
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">📝</div>
                <div>
                  <h3 className="font-medium text-gray-900 group-hover:text-purple-600">
                    Worksheets
                  </h3>
                  <p className="text-sm text-gray-500">Downloadable exercises</p>
                </div>
              </div>
            </Link>

            <Link
              href="/resources?category=crisis-resources"
              className="group p-4 rounded-lg border border-gray-200 hover:border-red-300 hover:bg-red-50 transition-all"
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">🆘</div>
                <div>
                  <h3 className="font-medium text-gray-900 group-hover:text-red-600">
                    Crisis Resources
                  </h3>
                  <p className="text-sm text-gray-500">Immediate help</p>
                </div>
              </div>
            </Link>

            <Link
              href="/resources?category=mindfulness-meditation"
              className="group p-4 rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all"
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">🧘</div>
                <div>
                  <h3 className="font-medium text-gray-900 group-hover:text-green-600">
                    Mindfulness
                  </h3>
                  <p className="text-sm text-gray-500">Meditation & relaxation</p>
                </div>
              </div>
            </Link>
          </div>
        </div>

        {/* All Resources */}
        <div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">All Resources</h2>
          <ResourceList />
        </div>
      </main>
    </div>
  );
}
